/**
 * SpectrumVisualizerModal v2 - Versione con gestione robusta canvas
 * Risolve definitivamente il problema "Canvas is already in use"
 */

class SpectrumVisualizerModal {
    constructor(canvasId) {
        this.canvasId = canvasId;
        this.chart = null;
        this.currentData = null;
        this.updateTimeout = null;
        
        console.log(`DEBUG: SpectrumVisualizerModal v2 inizializzato per canvas ${canvasId}`);
    }

    /**
     * Pulizia robusta del canvas - Metodo universale
     */
    cleanCanvas(canvas) {
        console.log(`DEBUG: Iniziando pulizia robusta canvas ${this.canvasId}`);
        
        try {
            // Metodo 1: Pulizia istanza interna
            if (this.chart) {
                console.log('DEBUG: Distruggendo this.chart...');
                this.chart.destroy();
                this.chart = null;
            }
        } catch (e) {
            console.warn('DEBUG: Errore pulizia this.chart:', e.message);
        }

        try {
            // Metodo 2: Chart.getChart (Chart.js v3+)
            if (typeof Chart.getChart === 'function') {
                const existingChart = Chart.getChart(canvas);
                if (existingChart) {
                    console.log(`DEBUG: Distruggendo via Chart.getChart (ID: ${existingChart.id})`);
                    existingChart.destroy();
                }
            }
        } catch (e) {
            console.warn('DEBUG: Errore Chart.getChart:', e.message);
        }

        try {
            // Metodo 3: Iterazione Chart.instances (Chart.js v2/v3)
            if (Chart.instances) {
                Object.keys(Chart.instances).forEach(key => {
                    const instance = Chart.instances[key];
                    if (instance && instance.canvas === canvas) {
                        console.log(`DEBUG: Distruggendo via Chart.instances (key: ${key})`);
                        instance.destroy();
                        delete Chart.instances[key];
                    }
                });
            }
        } catch (e) {
            console.warn('DEBUG: Errore Chart.instances:', e.message);
        }

        try {
            // Metodo 4: Pulizia attributi canvas
            if (canvas.chart) {
                console.log('DEBUG: Distruggendo canvas.chart...');
                canvas.chart.destroy();
                delete canvas.chart;
            }
            
            // Rimuovi attributi Chart.js
            delete canvas.chartjs;
            canvas.removeAttribute('data-chartjs-id');
            
            // Reset stili
            canvas.style.width = '';
            canvas.style.height = '';
            
            console.log('DEBUG: Attributi canvas puliti');
        } catch (e) {
            console.warn('DEBUG: Errore pulizia attributi:', e.message);
        }

        try {
            // Metodo 5: Clear context
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            console.log('DEBUG: Context canvas pulito');
        } catch (e) {
            console.warn('DEBUG: Errore pulizia context:', e.message);
        }

        console.log(`DEBUG: Pulizia robusta completata per ${this.canvasId}`);
    }

    /**
     * Crea grafico con pulizia robusta
     */
    createChart(data, options) {
        const canvas = document.getElementById(this.canvasId);
        if (!canvas) {
            console.error(`Canvas con ID ${this.canvasId} non trovato`);
            return;
        }

        console.log(`DEBUG: Creando grafico su canvas ${this.canvasId}`);

        // Pulizia robusta prima della creazione
        this.cleanCanvas(canvas);

        try {
            const ctx = canvas.getContext('2d');
            
            // Crea il nuovo grafico
            this.chart = new Chart(ctx, {
                type: 'line',
                data: data,
                options: options
            });

            console.log(`DEBUG: Grafico creato con successo su ${this.canvasId} (ID: ${this.chart.id})`);
            
        } catch (error) {
            console.error(`DEBUG: Errore creazione grafico su ${this.canvasId}:`, error);
            
            // Tentativo di recovery con pulizia più aggressiva
            try {
                console.log('DEBUG: Tentativo recovery con pulizia aggressiva...');
                
                // Pulizia aggressiva globale
                this.globalChartCleanup();
                
                // Retry creazione
                const ctx = canvas.getContext('2d');
                this.chart = new Chart(ctx, {
                    type: 'line',
                    data: data,
                    options: options
                });
                
                console.log(`DEBUG: Recovery riuscito su ${this.canvasId}`);
                
            } catch (recoveryError) {
                console.error(`DEBUG: Recovery fallito su ${this.canvasId}:`, recoveryError);
                throw recoveryError;
            }
        }
    }

    /**
     * Pulizia globale aggressiva di tutte le istanze Chart.js
     */
    globalChartCleanup() {
        console.log('DEBUG: Iniziando pulizia globale aggressiva...');
        
        try {
            // Distruggi tutte le istanze Chart.js globali
            if (window.Chart && Chart.instances) {
                const instances = Object.keys(Chart.instances);
                console.log(`DEBUG: Trovate ${instances.length} istanze Chart.js globali`);
                
                instances.forEach(key => {
                    try {
                        const instance = Chart.instances[key];
                        if (instance) {
                            console.log(`DEBUG: Distruggendo istanza globale ${key}`);
                            instance.destroy();
                        }
                        delete Chart.instances[key];
                    } catch (e) {
                        console.warn(`DEBUG: Errore distruzione istanza ${key}:`, e.message);
                    }
                });
            }
        } catch (e) {
            console.warn('DEBUG: Errore pulizia globale:', e.message);
        }
    }

    /**
     * Renderizza lo spettro elastico
     */
    renderElasticSpectrum(spectrumData) {
        console.log('Rendering spettro elastico modal v2:', spectrumData);
        
        this.currentData = spectrumData;
        
        const chartData = {
            labels: spectrumData.spectrum_points.map(point => point.T.toFixed(4)),
            datasets: [{
                label: 'Spettro Elastico Se(T)',
                data: spectrumData.spectrum_points.map(point => parseFloat(point.Se.toFixed(4))),
                borderColor: '#e74c3c',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        };
        
        const options = this.getChartOptions('Spettro di Risposta Elastico', 'Periodo T (s)', 'Accelerazione Se (g)');
        
        this.createChart(chartData, options);
        this.updateMetadata(spectrumData);
    }

    /**
     * Renderizza il confronto sovrapposto
     */
    renderComparison(comparisonData) {
        console.log('Rendering confronto spettri sovrapposti modal v2:', comparisonData);
        
        this.currentData = comparisonData;
        
        const beforeSpectrum = comparisonData.before_spectrum;
        const afterSpectrum = comparisonData.after_spectrum;
        
        const beforeField = beforeSpectrum.spectrum_points[0].hasOwnProperty('Sd') ? 'Sd' : 'Se';
        const afterField = afterSpectrum.spectrum_points[0].hasOwnProperty('Sd') ? 'Sd' : 'Se';
        
        const chartData = {
            labels: beforeSpectrum.spectrum_points.map(point => point.T.toFixed(4)),
            datasets: [
                {
                    label: `ANTE Intervento (ξ=${comparisonData.comparison_metadata.damping_before}%)`,
                    data: beforeSpectrum.spectrum_points.map(point => parseFloat(point[beforeField].toFixed(4))),
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 2,
                    pointHoverRadius: 6
                },
                {
                    label: `POST Intervento (ξ=${comparisonData.comparison_metadata.damping_after}%)`,
                    data: afterSpectrum.spectrum_points.map(point => parseFloat(point[afterField].toFixed(4))),
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 2,
                    pointHoverRadius: 6
                }
            ]
        };
        
        const options = this.getChartOptions('Confronto Spettri Prima/Dopo Dissipatori', 'Periodo T (s)', 'Accelerazione (g)');
        options.plugins.legend.display = true;
        options.plugins.legend.position = 'top';
        options.plugins.tooltip.callbacks.label = function(context) {
            return `${context.dataset.label}: ${context.parsed.y.toFixed(4)} g`;
        };
        
        this.createChart(chartData, options);
        this.updateComparisonMetadata(comparisonData);
    }

    /**
     * Configurazione opzioni Chart.js
     */
    getChartOptions(title, xLabel, yLabel) {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: title,
                    font: { size: 16, weight: 'bold' },
                    color: '#f8f9fa'
                },
                legend: {
                    display: true,
                    position: 'top',
                    labels: { color: '#f8f9fa' }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#f8f9fa',
                    bodyColor: '#f8f9fa',
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${context.parsed.y.toFixed(4)} g`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: { display: true, text: xLabel, color: '#f8f9fa' },
                    ticks: { color: '#adb5bd' },
                    grid: { color: 'rgba(255, 255, 255, 0.1)' }
                },
                y: {
                    display: true,
                    title: { display: true, text: yLabel, color: '#f8f9fa' },
                    beginAtZero: true,
                    ticks: { color: '#adb5bd' },
                    grid: { color: 'rgba(255, 255, 255, 0.1)' }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        };
    }

    /**
     * Aggiorna metadati
     */
    updateMetadata(spectrumData) {
        // Implementazione semplificata per test
        console.log('DEBUG: Aggiornamento metadati per', this.canvasId);
    }

    /**
     * Aggiorna metadati confronto
     */
    updateComparisonMetadata(comparisonData) {
        // Implementazione semplificata per test
        console.log('DEBUG: Aggiornamento metadati confronto per', this.canvasId);
    }

    /**
     * Distruggi istanza
     */
    destroy() {
        const canvas = document.getElementById(this.canvasId);
        if (canvas) {
            this.cleanCanvas(canvas);
        }
        
        if (this.updateTimeout) {
            clearTimeout(this.updateTimeout);
        }
        
        this.currentData = null;
        console.log(`DEBUG: SpectrumVisualizerModal v2 distrutto per ${this.canvasId}`);
    }
}

// Esporta la classe
window.SpectrumVisualizerModal = SpectrumVisualizerModal;
