/**
 * js/SpectrumVisualizerModal.js
 * Versione semplificata di SpectrumVisualizer per uso nel modal
 * Compatibile con caricamento dinamico senza ES6 modules
 * Versione: 1.0 - Data: 15/06/2025
 */

// Versione semplificata per il modal
window.SpectrumVisualizerModal = class {
    constructor(containerId) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.chart = null;
        this.currentData = null;
        
        if (!this.container) {
            throw new Error(`Container con ID '${containerId}' non trovato`);
        }
        
        // Verifica disponibilità Chart.js
        if (typeof Chart === 'undefined') {
            throw new Error('Chart.js richiesto per la visualizzazione');
        }
        
        this.initializeContainer();
    }
    
    /**
     * Inizializza il container per la visualizzazione
     */
    initializeContainer() {
        this.container.innerHTML = `
            <div class="spectrum-visualizer-modal">
                <div class="spectrum-controls">
                    <div class="control-group">
                        <label for="spectrum-type-select-modal">Tipo Spettro:</label>
                        <select id="spectrum-type-select-modal" class="form-control">
                            <option value="elastic">Elastico</option>
                            <option value="design">Progetto</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="q-factor-input-modal">Fattore q:</label>
                        <input type="number" id="q-factor-input-modal" class="form-control" 
                               min="1.0" max="6.5" step="0.1" value="1.0">
                    </div>
                    <div class="control-group">
                        <label for="damping-input-modal">Smorzamento (%):</label>
                        <input type="number" id="damping-input-modal" class="form-control" 
                               min="1" max="20" step="0.5" value="5.0">
                    </div>
                    <div class="control-group">
                        <button id="update-spectrum-btn-modal" class="btn btn-primary">
                            Aggiorna Spettro
                        </button>
                        <button id="export-chart-btn-modal" class="btn btn-secondary">
                            Esporta PNG
                        </button>
                    </div>
                </div>
                <div class="spectrum-chart-container">
                    <canvas id="spectrum-chart-modal"></canvas>
                </div>
                <div class="spectrum-info">
                    <div id="spectrum-metadata-modal" class="metadata-panel"></div>
                </div>
            </div>
        `;
        
        this.bindEvents();
    }
    
    /**
     * Collega gli eventi ai controlli
     */
    bindEvents() {
        const updateBtn = document.getElementById('update-spectrum-btn-modal');
        const exportBtn = document.getElementById('export-chart-btn-modal');
        const typeSelect = document.getElementById('spectrum-type-select-modal');
        
        if (updateBtn) updateBtn.addEventListener('click', () => this.updateSpectrum());
        if (exportBtn) exportBtn.addEventListener('click', () => this.exportChart());
        if (typeSelect) typeSelect.addEventListener('change', () => this.onSpectrumTypeChange());
        
        // Auto-update su cambio parametri
        const qInput = document.getElementById('q-factor-input-modal');
        const dampingInput = document.getElementById('damping-input-modal');
        
        if (qInput) qInput.addEventListener('input', () => this.debounceUpdate());
        if (dampingInput) dampingInput.addEventListener('input', () => this.debounceUpdate());
    }
    
    /**
     * Renderizza lo spettro elastico
     */
    renderElasticSpectrum(spectrumData) {
        console.log('Rendering spettro elastico modal:', spectrumData);

        this.currentData = spectrumData;

        const chartData = {
            labels: spectrumData.spectrum_points.map(point => point.T.toFixed(4)),
            datasets: [{
                label: 'Spettro Elastico Se(T)',
                data: spectrumData.spectrum_points.map(point => parseFloat(point.Se.toFixed(4))),
                borderColor: '#e74c3c',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        };

        const options = this.getChartOptions('Spettro di Risposta Elastico', 'Periodo T (s)', 'Accelerazione Se (g)');

        this.createChart(chartData, options);
        this.updateMetadata(spectrumData);
    }
    
    /**
     * Renderizza lo spettro di progetto
     */
    renderDesignSpectrum(spectrumData) {
        console.log('Rendering spettro di progetto modal:', spectrumData);

        this.currentData = spectrumData;

        const chartData = {
            labels: spectrumData.spectrum_points.map(point => point.T.toFixed(4)),
            datasets: [
                {
                    label: 'Spettro Elastico Se(T)',
                    data: spectrumData.elastic_spectrum.map(point => parseFloat(point.Se.toFixed(4))),
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    borderWidth: 1,
                    borderDash: [5, 5],
                    fill: false
                },
                {
                    label: `Spettro di Progetto Sd(T) (q=${spectrumData.parameters.q_factor})`,
                    data: spectrumData.spectrum_points.map(point => parseFloat(point.Sd.toFixed(4))),
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }
            ]
        };

        const options = this.getChartOptions('Spettro di Risposta di Progetto', 'Periodo T (s)', 'Accelerazione (g)');

        this.createChart(chartData, options);
        this.updateMetadata(spectrumData);
    }

    /**
     * Renderizza il confronto sovrapposto tra spettri ANTE e POST
     */
    renderComparison(comparisonData) {
        console.log('Rendering confronto spettri sovrapposti modal:', comparisonData);

        this.currentData = comparisonData;

        const beforeSpectrum = comparisonData.before_spectrum;
        const afterSpectrum = comparisonData.after_spectrum;

        // Determina quale campo usare (Se per elastico, Sd per progetto)
        const beforeField = beforeSpectrum.spectrum_points[0].hasOwnProperty('Sd') ? 'Sd' : 'Se';
        const afterField = afterSpectrum.spectrum_points[0].hasOwnProperty('Sd') ? 'Sd' : 'Se';

        const chartData = {
            labels: beforeSpectrum.spectrum_points.map(point => point.T.toFixed(4)),
            datasets: [
                {
                    label: `ANTE Intervento (ξ=${comparisonData.comparison_metadata.damping_before}%)`,
                    data: beforeSpectrum.spectrum_points.map(point => parseFloat(point[beforeField].toFixed(4))),
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 2,
                    pointHoverRadius: 6
                },
                {
                    label: `POST Intervento (ξ=${comparisonData.comparison_metadata.damping_after}%)`,
                    data: afterSpectrum.spectrum_points.map(point => parseFloat(point[afterField].toFixed(4))),
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 2,
                    pointHoverRadius: 6
                }
            ]
        };

        const options = this.getChartOptions('Confronto Spettri Prima/Dopo Dissipatori', 'Periodo T (s)', 'Accelerazione (g)');

        // Personalizza le opzioni per il confronto
        options.plugins.legend.display = true;
        options.plugins.legend.position = 'top';
        options.plugins.tooltip.callbacks.label = function(context) {
            return `${context.dataset.label}: ${context.parsed.y.toFixed(4)} g`;
        };

        this.createChart(chartData, options);
        this.updateComparisonMetadata(comparisonData);
    }

    /**
     * Crea o aggiorna il grafico Chart.js
     */
    createChart(data, options) {
        const canvas = document.getElementById(this.canvasId);
        if (!canvas) {
            console.error(`Canvas con ID ${this.canvasId} non trovato`);
            return;
        }

        console.log(`DEBUG: Iniziando creazione grafico su canvas ${this.canvasId}`);

        // PULIZIA AGGRESSIVA - Metodo 1: Pulizia interna classe
        if (this.chart) {
            console.log('DEBUG: Distruggendo grafico esistente (this.chart)...');
            try {
                this.chart.destroy();
            } catch (e) {
                console.warn('DEBUG: Errore distruzione this.chart:', e.message);
            }
            this.chart = null;
        }

        // PULIZIA AGGRESSIVA - Metodo 2: Pulizia Chart.js globale
        try {
            const existingChart = Chart.getChart(canvas);
            if (existingChart) {
                console.log(`DEBUG: Distruggendo grafico Chart.js esistente (ID: ${existingChart.id}) sul canvas...`);
                existingChart.destroy();
            }
        } catch (e) {
            console.warn('DEBUG: Errore pulizia Chart.js globale:', e.message);
        }

        // PULIZIA AGGRESSIVA - Metodo 3: Pulizia forzata tutti i grafici Chart.js
        try {
            Chart.helpers.each(Chart.instances, (instance, id) => {
                if (instance.canvas === canvas) {
                    console.log(`DEBUG: Distruggendo istanza Chart.js forzata (ID: ${id})`);
                    instance.destroy();
                }
            });
        } catch (e) {
            console.warn('DEBUG: Errore pulizia forzata istanze:', e.message);
        }

        // PULIZIA AGGRESSIVA - Metodo 4: Reset canvas context
        try {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            console.log('DEBUG: Canvas context pulito');
        } catch (e) {
            console.warn('DEBUG: Errore pulizia canvas context:', e.message);
        }

        // Crea il nuovo grafico dopo pulizia completa
        try {
            const ctx = canvas.getContext('2d');

            // Verifica finale che non ci siano grafici residui
            const finalCheck = Chart.getChart(canvas);
            if (finalCheck) {
                console.log(`DEBUG: Trovato grafico residuo (ID: ${finalCheck.id}), distruggendo...`);
                finalCheck.destroy();
            }

            // Crea il nuovo grafico
            this.chart = new Chart(ctx, {
                type: 'line',
                data: data,
                options: options
            });

            console.log(`DEBUG: Grafico creato con successo su canvas ${this.canvasId} (ID: ${this.chart.id})`);
        } catch (error) {
            console.error(`DEBUG: Errore creazione grafico su ${this.canvasId}:`, error);

            // Tentativo di recovery: pulizia forzata e retry
            try {
                console.log('DEBUG: Tentativo di recovery...');
                this.forceCanvasCleanup(canvas);

                const ctx = canvas.getContext('2d');
                this.chart = new Chart(ctx, {
                    type: 'line',
                    data: data,
                    options: options
                });

                console.log(`DEBUG: Recovery riuscito su canvas ${this.canvasId}`);
            } catch (recoveryError) {
                console.error(`DEBUG: Recovery fallito su ${this.canvasId}:`, recoveryError);
                throw recoveryError;
            }
        }
    }

    /**
     * Pulizia forzata del canvas per situazioni di recovery
     */
    forceCanvasCleanup(canvas) {
        console.log('DEBUG: Iniziando pulizia forzata canvas...');

        try {
            // Metodo 1: Distruggi tutte le istanze Chart.js associate al canvas
            const allCharts = Object.values(Chart.instances || {});
            allCharts.forEach(chart => {
                if (chart && chart.canvas === canvas) {
                    console.log(`DEBUG: Distruggendo istanza forzata ${chart.id}`);
                    chart.destroy();
                }
            });
        } catch (e) {
            console.warn('DEBUG: Errore pulizia istanze forzata:', e.message);
        }

        try {
            // Metodo 2: Reset completo canvas
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Reset attributi canvas
            canvas.style.width = '';
            canvas.style.height = '';

            console.log('DEBUG: Canvas reset completato');
        } catch (e) {
            console.warn('DEBUG: Errore reset canvas:', e.message);
        }

        try {
            // Metodo 3: Rimuovi attributi Chart.js dal canvas
            delete canvas.chartjs;
            canvas.removeAttribute('data-chartjs-id');
            console.log('DEBUG: Attributi Chart.js rimossi dal canvas');
        } catch (e) {
            console.warn('DEBUG: Errore rimozione attributi:', e.message);
        }
    }

    /**
     * Configurazione opzioni Chart.js
     */
    getChartOptions(title, xLabel, yLabel) {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    color: '#f8f9fa'
                },
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        color: '#f8f9fa'
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#f8f9fa',
                    bodyColor: '#f8f9fa',
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${context.parsed.y.toFixed(4)} g`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: xLabel,
                        color: '#f8f9fa'
                    },
                    ticks: {
                        color: '#adb5bd'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: yLabel,
                        color: '#f8f9fa'
                    },
                    beginAtZero: true,
                    ticks: {
                        color: '#adb5bd'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        };
    }
    
    /**
     * Aggiorna i metadati visualizzati
     */
    updateMetadata(spectrumData) {
        // Cerca il container metadati associato al canvas
        let metadataPanel = document.getElementById('spectrum-metadata-modal');

        // Se non trovato, cerca container alternativo per i risultati
        if (!metadataPanel && this.canvasId === 'spectrum-comparison-chart-results') {
            metadataPanel = document.getElementById('spectrum-comparison-metadata');
        }

        if (!metadataPanel) {
            console.warn(`Container metadati non trovato per canvas ${this.canvasId}`);
            return;
        }
        
        const metadata = spectrumData.metadata || {};
        const amplification = spectrumData.amplification_coefficients || {};
        const periods = spectrumData.characteristic_periods || {};
        
        metadataPanel.innerHTML = `
            <h4>Parametri Calcolo</h4>
            <div class="metadata-grid">
                <div class="param-item">
                    <span class="param-label">Tipo:</span>
                    <span class="param-value">${metadata.calculation_type || 'N/A'}</span>
                </div>
                <div class="param-item">
                    <span class="param-label">Smorzamento:</span>
                    <span class="param-value">${metadata.damping_ratio || 'N/A'}%</span>
                </div>
                <div class="param-item">
                    <span class="param-label">Categoria Suolo:</span>
                    <span class="param-value">${metadata.soil_category || 'N/A'}</span>
                </div>
                <div class="param-item">
                    <span class="param-label">SS:</span>
                    <span class="param-value">${amplification.SS || 'N/A'}</span>
                </div>
                <div class="param-item">
                    <span class="param-label">CC:</span>
                    <span class="param-value">${amplification.CC || 'N/A'}</span>
                </div>
                <div class="param-item">
                    <span class="param-label">S:</span>
                    <span class="param-value">${amplification.S || 'N/A'}</span>
                </div>
                <div class="param-item">
                    <span class="param-label">TB:</span>
                    <span class="param-value">${periods.TB || 'N/A'} s</span>
                </div>
                <div class="param-item">
                    <span class="param-label">TC:</span>
                    <span class="param-value">${periods.TC || 'N/A'} s</span>
                </div>
                <div class="param-item">
                    <span class="param-label">TD:</span>
                    <span class="param-value">${periods.TD || 'N/A'} s</span>
                </div>
            </div>
        `;
    }

    /**
     * Aggiorna i metadati per il confronto sovrapposto
     */
    updateComparisonMetadata(comparisonData) {
        // Cerca il container metadati associato al canvas
        let metadataPanel = document.getElementById('spectrum-metadata-modal');

        // Se non trovato, cerca container alternativo per i risultati
        if (!metadataPanel && this.canvasId === 'spectrum-comparison-chart-results') {
            metadataPanel = document.getElementById('spectrum-comparison-metadata');
        }

        if (!metadataPanel) {
            console.warn(`Container metadati non trovato per canvas ${this.canvasId}`);
            return;
        }

        const reductionAnalysis = comparisonData.reduction_analysis || {};
        const metadata = comparisonData.comparison_metadata || {};

        metadataPanel.innerHTML = `
            <h4>Analisi Confronto Spettri</h4>
            <div class="comparison-stats">
                <div class="stat-item highlight">
                    <span class="stat-label">📉 Riduzione Media:</span>
                    <span class="stat-value">${(reductionAnalysis.average_reduction || 0).toFixed(4)}%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">📊 Riduzione Massima:</span>
                    <span class="stat-value">${(reductionAnalysis.max_reduction || 0).toFixed(4)}%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">📈 Riduzione Minima:</span>
                    <span class="stat-value">${(reductionAnalysis.min_reduction || 0).toFixed(4)}%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">🔴 Smorzamento ANTE:</span>
                    <span class="stat-value">${(metadata.damping_before || 0).toFixed(4)}%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">🟢 Smorzamento POST:</span>
                    <span class="stat-value">${(metadata.damping_after || 0).toFixed(4)}%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">⚡ Efficienza Sistema:</span>
                    <span class="stat-value">${(reductionAnalysis.efficiency || 0).toFixed(4)}%</span>
                </div>
            </div>

            <div class="comparison-note" style="margin-top: 1rem; padding: 1rem; background: rgba(39, 174, 96, 0.1); border-left: 4px solid #27ae60; border-radius: 4px;">
                <p style="margin: 0; color: #27ae60; font-weight: 600;">
                    <i class="fas fa-check-circle"></i>
                    I dissipatori riducono efficacemente le accelerazioni spettrali, migliorando la risposta sismica della struttura.
                </p>
            </div>
        `;
    }

    /**
     * Gestisce il cambio di tipo spettro
     */
    onSpectrumTypeChange() {
        const typeSelect = document.getElementById('spectrum-type-select-modal');
        const qFactorInput = document.getElementById('q-factor-input-modal');
        
        if (!typeSelect || !qFactorInput) return;
        
        // Abilita/disabilita fattore q in base al tipo
        if (typeSelect.value === 'elastic') {
            qFactorInput.disabled = true;
            qFactorInput.value = '1.0';
        } else {
            qFactorInput.disabled = false;
        }
    }
    
    /**
     * Aggiorna lo spettro con i parametri correnti
     */
    updateSpectrum() {
        if (!this.currentData) {
            console.warn('Nessun dato spettro disponibile per l\'aggiornamento');
            return;
        }
        
        console.log('Aggiornamento spettro richiesto');
        // Implementazione semplificata - per ora ricarica lo stesso spettro
        this.renderElasticSpectrum(this.currentData);
    }
    
    /**
     * Debounce per aggiornamenti automatici
     */
    debounceUpdate() {
        clearTimeout(this.updateTimeout);
        this.updateTimeout = setTimeout(() => {
            this.updateSpectrum();
        }, 500);
    }
    
    /**
     * Esporta il grafico come PNG
     */
    exportChart() {
        if (!this.chart) {
            console.warn('Nessun grafico disponibile per l\'esportazione');
            return;
        }
        
        const url = this.chart.toBase64Image();
        const link = document.createElement('a');
        link.download = `spettro_risposta_${new Date().getTime()}.png`;
        link.href = url;
        link.click();
    }
    
    /**
     * Pulisce le risorse
     */
    destroy() {
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }
        
        if (this.updateTimeout) {
            clearTimeout(this.updateTimeout);
        }
        
        this.currentData = null;
    }
};
