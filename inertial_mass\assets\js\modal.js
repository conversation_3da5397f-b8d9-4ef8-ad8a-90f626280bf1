/**
 * modal.js - Logica della finestra modale massa inerziale
 * Path: /inertial_mass/assets/js/modal.js
 */

// Stato globale del modulo
let inertialMassState = {
    seismicData: null,
    floors: [],
    isCalculating: false
};

/**
 * Formatta la categoria del suolo con la descrizione completa
 * @param {string} category - Codice categoria del suolo (A, B, C, D, E)
 * @returns {string} Categoria formattata con descrizione
 */
function formatSoilCategory(category) {
    const soilCategories = {
        'A': 'A - Ammassi rocciosi',
        'B': 'B - Rocce tenere e depositi',
        'C': 'C - Depositi mediamente addensati',
        'D': 'D - Depositi scarsamente addensati',
        'E': 'E - Terreni con caratteristiche speciali'
    };
    
    return soilCategories[category] || category || '-';
}

/**
 * Inizializza il modulo massa inerziale
 * @param {Object} seismicData - Dati sismici da ASDP
 */
function initInertialMassModal(seismicData) {
    console.log('DEBUG: initInertialMassModal - INIZIO');
    console.log('DEBUG: Dati sismici ricevuti:', seismicData);
    console.log('MODAL_JS: initInertialMassModal chiamata - aggiornamento in corso...');
    
    try {
        // Salva i dati sismici
        console.log('DEBUG: Salvando dati sismici nello stato');
        inertialMassState.seismicData = seismicData;
        
        // Verifica che la modale esista
        console.log('DEBUG: Verificando esistenza elemento modale');
        const modalElement = document.getElementById('inertialMassModal');
        if (!modalElement) {
            console.error('ERRORE CRITICO: Elemento modale non trovato');
            return;
        }
        console.log('DEBUG: Elemento modale trovato');
        
        // Funzione di utilità per impostare il testo con controllo di sicurezza
        function setElementText(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                console.log(`DEBUG: Impostato ${id} = ${value}`);
            } else {
                console.warn(`DEBUG: Elemento ${id} non trovato`);
            }
        }
        
        // Popola i dati automatici con controlli di sicurezza
        console.log('DEBUG: Popolando dati sismici nella modale');
        console.log('MODAL_JS: Aggiornamento dati sismici nel modulo...');
        console.log('MODAL_JS: Dati ricevuti:', {
            lat: seismicData.lat,
            lon: seismicData.lon,
            zone: seismicData.zone,
            ag: seismicData.ag,
            F0: seismicData.F0,
            TC: seismicData.TC,
            soil_category: seismicData.soil_category
        });
        
        setElementText('im-coordinates', `${seismicData.lat.toFixed(4)}, ${seismicData.lon.toFixed(4)}`);
        setElementText('im-seismic-zone', seismicData.zone || '-');
        setElementText('im-ag', seismicData.ag || '-');
        setElementText('im-f0', seismicData.F0 || '-');
        setElementText('im-tc', seismicData.TC || '-');
        
        // Debug della categoria del suolo
        console.log('Categoria del suolo ricevuta:', seismicData.soil_category);
        
        // Formatta la categoria del suolo con descrizione completa
        const soilCategoryText = formatSoilCategory(seismicData.soil_category);
        console.log('Categoria del suolo formattata:', soilCategoryText);
        setElementText('im-soil-category', soilCategoryText);

        // Popola Smorz. Viscoso Eq. (η)
        setElementText('im-damping', seismicData.damping ? seismicData.damping.toFixed(1) + '%' : '-');
        console.log(`DEBUG: Impostato im-damping = ${seismicData.damping ? seismicData.damping.toFixed(1) + '%' : '-'}`);

        // Popola Fattore di Struttura (q0)
        setElementText('im-q0-factor', seismicData.q_factor ? seismicData.q_factor.toFixed(1) : '-');
        console.log(`DEBUG: Impostato im-q0-factor = ${seismicData.q_factor ? seismicData.q_factor.toFixed(1) : '-'}`);

        console.log('DEBUG: Reset del form e stato');
        // Reset e inizializza con un piano (pulisce eventuali piani esistenti)
        inertialMassState.floors = [];
        const floorsContainer = document.getElementById('floors-container');
        if (floorsContainer) {
            floorsContainer.innerHTML = ''; // Pulisce i piani esistenti dal DOM
            console.log('DEBUG: Container piani svuotato');
        } else {
            console.warn('DEBUG: Container piani non trovato');
        }
        
        console.log('DEBUG: Aggiungendo primo piano');
        addFloor(); // Aggiunge il primo piano
        
        console.log('DEBUG: Nascondendo risultati e mostrando form');
        // Nascondi risultati e mostra form
        const resultsSection = document.getElementById('results-section');
        if (resultsSection) {
            resultsSection.style.display = 'none';
            console.log('DEBUG: Sezione risultati nascosta');
        }
        
        const modalBody = document.querySelector('.modal-body');
        if (modalBody) {
            modalBody.style.display = 'block';
            console.log('DEBUG: Modal body mostrato');
        }
        
        // Inizializza gli event listeners per il form
        // IMPORTANTE: Deve essere chiamato dopo il caricamento della modale
        console.log('DEBUG: Inizializzando event listeners');
        initFormEventListeners();

        // Inizializza event listener sicuro per pulsante chiusura
        initSafeCloseButton();

        // Inizializza sistema di tab
        console.log('DEBUG: Inizializzando sistema tab');
        initTabSystem();

        // Reset stato iniziale
        resetConstructionTypeFields();

        // Inizializza la gestione delle tipologie costruttive con delay per assicurare DOM pronto
        setTimeout(() => {
            console.log('DEBUG: Inizializzando gestione tipologie costruttive (con delay)');
            initConstructionTypeHandlers();

            // Forza aggiornamento se c'è già un valore selezionato
            const categorySelect = document.getElementById('im-construction-category');
            if (categorySelect && categorySelect.value && categorySelect.value !== '') {
                console.log('DEBUG: Valore già presente alla apertura, forzando aggiornamento:', categorySelect.value);
                handleCategoryChange({ target: categorySelect });
            }
        }, 150);

        // Test aggiuntivo più aggressivo
        setTimeout(() => {
            console.log('DEBUG: Test aggressivo event listener...');
            const categorySelect = document.getElementById('im-construction-category');
            if (categorySelect) {
                // Forza il trigger dell'evento se c'è già un valore selezionato
                if (categorySelect.value) {
                    console.log('DEBUG: Valore già selezionato, forzando aggiornamento:', categorySelect.value);
                    handleCategoryChange({ target: categorySelect });
                }
            }
        }, 500);

        console.log('Modale massa inerziale inizializzata completamente');
        
        // Mostra la modale
        modalElement.style.display = 'flex';

        // FORZA VISIBILITÀ - Applica stili inline per evitare conflitti CSS
        modalElement.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0, 0, 0, 0.7) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            z-index: 99999 !important;
            visibility: visible !important;
            opacity: 1 !important;
            font-family: 'Segoe UI', Arial, sans-serif !important;
        `;

        // Forza visibilità del container
        const container = modalElement.querySelector('.modal-container');
        if (container) {
            container.style.cssText = `
                background-color: #1E1E1E !important;
                color: #f8f9fa !important;
                border: 1px solid #555e67 !important;
                border-radius: 0.3rem !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
                display: flex !important;
                flex-direction: column !important;
                width: 90% !important;
                max-width: 800px !important;
                max-height: 85vh !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: relative !important;
            `;
        }

        // Forza visibilità del body
        const body = modalElement.querySelector('.modal-body');
        if (body) {
            body.style.cssText = `
                background-color: #1E1E1E !important;
                color: #f8f9fa !important;
                padding: 1rem 1.5rem !important;
                overflow-y: auto !important;
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                flex: 1 1 auto !important;
            `;
        }

        // Forza visibilità di tutti gli elementi form
        const formElements = modalElement.querySelectorAll('input, select, label, button, .form-section, .form-group, .info-grid, .info-item, h3, span');
        formElements.forEach(element => {
            element.style.visibility = 'visible !important';
            element.style.opacity = '1 !important';
            element.style.color = '#f8f9fa !important';
            element.style.fontFamily = 'Segoe UI, Arial, sans-serif !important';

            if (element.tagName === 'INPUT' || element.tagName === 'SELECT') {
                element.style.backgroundColor = '#404040 !important';
                element.style.border = '1px solid #555e67 !important';
                element.style.padding = '0.65rem 0.75rem !important';
                element.style.borderRadius = '0.25rem !important';
                element.style.width = '100% !important';
                element.style.display = 'block !important';
            }

            if (element.tagName === 'BUTTON') {
                element.style.backgroundColor = '#D97706 !important';
                element.style.color = 'white !important';
                element.style.border = '1px solid #C26A05 !important';
                element.style.padding = '0.5rem 1rem !important';
                element.style.borderRadius = '0.25rem !important';
                element.style.cursor = 'pointer !important';
                element.style.display = 'inline-block !important';
            }
        });

        console.log('DEBUG: Visibilità forzata applicata a tutti gli elementi');

        // Debug della visibilità dopo apertura
        setTimeout(() => {
            console.log('DEBUG: Verificando stato modal dopo apertura...');
            if (typeof window.debugModalVisibility === 'function') {
                window.debugModalVisibility();
            }

            // Test finale degli event listener
            setTimeout(() => {
                testConstructionTypeHandlers();
            }, 100);
        }, 300);

        // Test immediato della funzione di chiusura
        console.log('🧪 DEBUG: Test disponibilità funzione closeInertialMassModal');
        console.log('🧪 DEBUG: window.closeInertialMassModal:', typeof window.closeInertialMassModal);
        console.log('🧪 DEBUG: closeInertialMassModal:', typeof closeInertialMassModal);

        console.log('DEBUG: initInertialMassModal completata con successo');

    } catch (error) {
        console.error('ERRORE CRITICO in initInertialMassModal:', error);
        console.error('ERRORE Stack trace:', error.stack);
        alert('Errore durante l\'inizializzazione della modale. Controlla la console.');
    } finally {
        console.log('DEBUG: initInertialMassModal - FINE');
    }
}

/**
 * Chiude la modale
 */
function closeInertialMassModal() {
    console.log('🔴 DEBUG: closeInertialMassModal chiamata');
    console.log('🔴 DEBUG: Tipo di this:', typeof this);
    console.log('🔴 DEBUG: Argomenti ricevuti:', arguments);

    const modalElement = document.getElementById('inertialMassModal');
    console.log('🔴 DEBUG: Modal element trovato:', modalElement ? 'SI' : 'NO');

    if (modalElement) {
        console.log('🔴 DEBUG: Display corrente:', modalElement.style.display);

        // SOLUZIONE: Nascondi il modal mantenendo la struttura CSS
        console.log('🔴 DEBUG: Nascondendo modal...');
        modalElement.style.display = 'none';
        modalElement.style.visibility = 'hidden';
        modalElement.style.opacity = '0';
        console.log('🔴 DEBUG: Modal nascosto correttamente');

        // RIMOSSO resetCalculation() CHE RIAPRIVA IL MODAL
        console.log('🔴 DEBUG: resetCalculation NON chiamato per evitare riapertura modal');

        console.log('🔴 DEBUG: Chiusura modale completata con successo');
    } else {
        console.warn('🔴 DEBUG: Elemento modale non trovato durante la chiusura');
    }
}

/**
 * Aggiunge un nuovo piano
 */
function addFloor() {
    console.log('Aggiunta nuovo piano');
    
    const floorsContainer = document.getElementById('floors-container');
    if (!floorsContainer) {
        console.error('Elemento container piani non trovato');
        return;
    }
    
    const floorNumber = inertialMassState.floors.length + 1;
    const floorId = `floor-${Date.now()}`;
    
    inertialMassState.floors.push({
        id: floorId,
        number: floorNumber
    });
    
    const floorHtml = `
        <div class="floor-item" data-floor-id="${floorId}">
            <div class="floor-header">
                <h4>Piano ${floorNumber}</h4>
                ${floorNumber > 1 ? `<button type="button" class="btn-remove-floor" onclick="removeFloor('${floorId}')">Rimuovi</button>` : ''}
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="${floorId}-area">Area (m²) *</label>
                    <input type="number" id="${floorId}-area" name="floors[${floorId}][area]" 
                           min="1" step="0.01" required placeholder="es. 120">
                </div>
                <div class="form-group">
                    <label for="${floorId}-height">Altezza interpiano (m) *</label>
                    <input type="number" id="${floorId}-height" name="floors[${floorId}][height]" 
                           min="0.1" step="0.01" required placeholder="es. 3.5">
                </div>
                <div class="form-group">
                    <label for="${floorId}-use">Destinazione d'uso *</label>
                    <select id="${floorId}-use" name="floors[${floorId}][use]" required>
                        <option value="">Seleziona...</option>
                        ${getUseOptionsForCurrentCategory()}
                    </select>
                </div>
            </div>
        </div>
    `;
    
    floorsContainer.insertAdjacentHTML('beforeend', floorHtml);
}

/**
 * Rimuove un piano
 * @param {string} floorId - ID del piano da rimuovere
 */
function removeFloor(floorId) {
    // Rimuovi dall'array
    inertialMassState.floors = inertialMassState.floors.filter(f => f.id !== floorId);
    
    // Rimuovi dal DOM
    const floorElement = document.querySelector(`[data-floor-id="${floorId}"]`);
    if (floorElement) {
        floorElement.remove();
    }
    
    // Rinumera i piani
    updateFloorNumbers();
}

/**
 * Aggiorna la numerazione dei piani
 */
function updateFloorNumbers() {
    const floorElements = document.querySelectorAll('.floor-item');
    floorElements.forEach((element, index) => {
        const header = element.querySelector('.floor-header h4');
        if (header) {
            header.textContent = `Piano ${index + 1}`;
        }
    });
}



/**
 * Inizializza event listener sicuro per pulsante chiusura
 * Aggiunge event listener diretto senza rimuovere onclick
 */
function initSafeCloseButton() {
    console.log('DEBUG: initSafeCloseButton - INIZIO');

    // Trova tutti i pulsanti di chiusura
    const closeButtons = document.querySelectorAll('.modal-close, [onclick*="closeInertialMassModal"]');
    console.log('DEBUG: Pulsanti chiusura trovati:', closeButtons.length);

    closeButtons.forEach((button, index) => {
        console.log(`DEBUG: Configurando pulsante chiusura ${index + 1}`);

        // Aggiungi event listener SENZA rimuovere onclick
        // Questo garantisce che almeno uno dei due metodi funzioni
        button.addEventListener('click', function(e) {
            console.log('DEBUG: Event listener pulsante chiusura attivato');

            // Prova prima la funzione globale
            if (typeof window.closeInertialMassModal === 'function') {
                console.log('DEBUG: Usando funzione globale');
                window.closeInertialMassModal();
            } else {
                console.log('DEBUG: Funzione globale non disponibile, chiusura diretta');
                // Fallback: chiusura diretta con rimozione stili forzati
                const modal = document.getElementById('inertialMassModal');
                if (modal) {
                    modal.removeAttribute('style');
                    modal.style.display = 'none';
                }
            }
        });

        console.log(`DEBUG: Event listener sicuro aggiunto al pulsante ${index + 1}`);
    });

    // Aggiungi anche listener per ESC key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('inertialMassModal');
            if (modal && modal.style.display === 'flex') {
                console.log('DEBUG: ESC premuto, chiudendo modal');
                if (typeof window.closeInertialMassModal === 'function') {
                    window.closeInertialMassModal();
                } else {
                    modal.removeAttribute('style');
                    modal.style.display = 'none';
                }
            }
        }
    });

    console.log('DEBUG: initSafeCloseButton - FINE');
}

/**
 * Inizializza gli event listeners per il form
 * Chiamata dopo il caricamento dinamico della modale
 */
function initFormEventListeners() {
    console.log('DEBUG: initFormEventListeners - INIZIO');

    const form = document.getElementById('inertialMassForm');
    console.log('DEBUG: Form trovato:', form ? 'SI' : 'NO');
    
    if (form) {
        console.log('DEBUG: Form type:', form.tagName);
        console.log('DEBUG: Form action:', form.action);
        console.log('DEBUG: Form method:', form.method);
        
        // Rimuovi eventuali listener esistenti per evitare duplicati
        form.removeEventListener('submit', handleFormSubmit);
        console.log('DEBUG: Listener precedenti rimossi');
        
        // Aggiungi il nuovo listener
        form.addEventListener('submit', handleFormSubmit);
        console.log('DEBUG: Nuovo event listener aggiunto');
        
        // Verifica che il listener sia stato aggiunto (getEventListeners non disponibile in produzione)
        console.log('DEBUG: Event listener aggiunto al form submit');
        
        console.log('Event listener per form aggiunto correttamente');
    } else {
        console.error('ERRORE: Form inertialMassForm non trovato');
        console.log('DEBUG: Elementi disponibili con ID:', 
            Array.from(document.querySelectorAll('[id]')).map(el => el.id));
    }
    
    console.log('DEBUG: initFormEventListeners - FINE');
}

/**
 * Gestisce il submit del form
 * @param {Event} e - Evento submit
 */
async function handleFormSubmit(e) {
    console.log('DEBUG: handleFormSubmit - INIZIO');
    console.log('DEBUG: Evento ricevuto:', e);
    console.log('DEBUG: Tipo evento:', e.type);
    console.log('DEBUG: Target:', e.target);
    
    try {
        console.log('DEBUG: Chiamando preventDefault()');
        e.preventDefault();
        console.log('DEBUG: preventDefault() eseguito con successo');
        
        console.log('Form submit intercettato - preventDefault applicato');
        
        if (inertialMassState.isCalculating) {
            console.log('DEBUG: Calcolo già in corso, uscita anticipata');
            return;
        }
        
        console.log('DEBUG: Impostando stato isCalculating = true');
        inertialMassState.isCalculating = true;
        
        console.log('DEBUG: Raccogliendo dati del form');
        // Raccogli i dati del form
        const formData = collectFormData();
        console.log('DEBUG: Dati raccolti:', formData);
        
        console.log('DEBUG: Validando dati del form');
        // Valida i dati
        if (!validateFormData(formData)) {
            console.log('DEBUG: Validazione fallita, ripristino stato');
            inertialMassState.isCalculating = false;
            return;
        }
        
        console.log('DEBUG: Validazione superata, chiamando calculateWithLLM');
        // Invia al LLM per il calcolo
        await calculateWithLLM(formData);
        
        console.log('DEBUG: calculateWithLLM completato');
        
    } catch (error) {
        console.error('ERRORE CRITICO in handleFormSubmit:', error);
        console.error('ERRORE Stack trace:', error.stack);
        
        // Ripristina lo stato
        inertialMassState.isCalculating = false;
        
        // Mostra errore all'utente
        alert('Si è verificato un errore durante l\'elaborazione. Controlla la console per dettagli.');
        
        // Previeni qualsiasi comportamento di default
        if (e && e.preventDefault) {
            e.preventDefault();
        }
        if (e && e.stopPropagation) {
            e.stopPropagation();
        }
        
        return false;
    } finally {
        console.log('DEBUG: handleFormSubmit - FINE');
    }
}

/**
 * Raccoglie i dati dal form
 * @returns {Object} Dati del form
 */
function collectFormData() {
    console.log('DEBUG: collectFormData - INIZIO');
    
    try {
        console.log('DEBUG: Stato seismicData:', inertialMassState.seismicData);
        
        const formData = {
            // Dati sismici da ASDP
            location: {
                lat: inertialMassState.seismicData.lat,
                lon: inertialMassState.seismicData.lon
            },
            seismic_params: {
                zone: inertialMassState.seismicData.zone,
                ag: inertialMassState.seismicData.ag,
                F0: inertialMassState.seismicData.F0,
                TC: inertialMassState.seismicData.TC,
                soil_category: inertialMassState.seismicData.soil_category,
                damping: inertialMassState.seismicData.damping, // Aggiunto smorzamento
                q_factor: inertialMassState.seismicData.q_factor // Aggiunto fattore di struttura
            },
            // Dati struttura
            building: {
                construction_category: document.getElementById('im-construction-category')?.value,
                structure_type: document.getElementById('im-structure-type')?.value,
                slab_type: document.getElementById('im-slab-type')?.value,
                construction_year: parseInt(document.getElementById('im-construction-year')?.value) || 0,
                floors: []
            }
        };
        
        console.log('DEBUG: Dati base raccolti:', formData);
        
        // Raccogli dati dei piani
        console.log('DEBUG: Numero piani nello stato:', inertialMassState.floors.length);
        
        inertialMassState.floors.forEach((floor, index) => {
            console.log(`DEBUG: Elaborando piano ${index + 1} con ID: ${floor.id}`);
            
            const areaEl = document.getElementById(`${floor.id}-area`);
            const heightEl = document.getElementById(`${floor.id}-height`);
            const useEl = document.getElementById(`${floor.id}-use`);
            
            console.log(`DEBUG: Piano ${index + 1} - elementi trovati:`, {
                area: areaEl ? 'SI' : 'NO',
                height: heightEl ? 'SI' : 'NO',
                use: useEl ? 'SI' : 'NO'
            });
            
            const area = parseFloat(areaEl?.value) || 0;
            const height = parseFloat(heightEl?.value) || 0;
            const use = useEl?.value || '';
            
            const floorData = {
                level: index + 1,
                area: area,
                height: height,
                use: use
            };
            
            console.log(`DEBUG: Piano ${index + 1} - dati raccolti:`, floorData);
            formData.building.floors.push(floorData);
        });
        
        console.log('DEBUG: Dati completi raccolti:', formData);
        return formData;
        
    } catch (error) {
        console.error('ERRORE CRITICO in collectFormData:', error);
        console.error('ERRORE Stack trace:', error.stack);
        throw error;
    } finally {
        console.log('DEBUG: collectFormData - FINE');
    }
}

/**
 * Valida i dati del form
 * @param {Object} formData - Dati da validare
 * @returns {boolean} True se validi
 */
function validateFormData(formData) {
    console.log('DEBUG: validateFormData - INIZIO');
    console.log('DEBUG: Dati da validare:', formData);
    
    try {
        // Verifica campi obbligatori
        console.log('DEBUG: Verificando campi obbligatori');

        // Verifica categoria costruttiva (sempre obbligatoria)
        if (!formData.building.construction_category) {
            alert('Seleziona la tipologia costruttiva');
            return false;
        }

        // Verifica sottocategorie solo se visibili
        const structureDiv = document.getElementById('structure-subcategory');
        const slabDiv = document.getElementById('slab-subcategory');

        if (structureDiv && structureDiv.style.display !== 'none' && !formData.building.structure_type) {
            alert('Seleziona la tipologia strutturale');
            return false;
        }

        if (slabDiv && slabDiv.style.display !== 'none' && !formData.building.slab_type) {
            alert('Seleziona la tipologia solaio/impalcato');
            return false;
        }

        // Verifica anno costruzione (sempre obbligatorio)
        if (!formData.building.construction_year) {
            alert('Inserisci l\'anno di costruzione');
            return false;
        }

        console.log('DEBUG: Campi obbligatori verificati:', {
            construction_category: formData.building.construction_category,
            structure_type: formData.building.structure_type,
            slab_type: formData.building.slab_type,
            construction_year: formData.building.construction_year
        });
        
        // Verifica almeno un piano
        console.log(`DEBUG: Verificando presenza piani - trovati: ${formData.building.floors.length}`);
        if (formData.building.floors.length === 0) {
            console.log('DEBUG: Nessun piano trovato');
            alert('Aggiungi almeno un piano');
            return false;
        }
        
        // Verifica dati dei piani
        console.log('DEBUG: Verificando dati dei piani');
        for (let floor of formData.building.floors) {
            console.log(`DEBUG: Validando piano ${floor.level}:`, floor);
            
            if (!floor.area || floor.area <= 0) {
                console.log(`DEBUG: Piano ${floor.level} - area non valida: ${floor.area}`);
                alert(`Piano ${floor.level}: L'area deve essere maggiore di 0`);
                return false;
            }
            
            if (!floor.height || floor.height <= 0) {
                console.log(`DEBUG: Piano ${floor.level} - altezza non valida: ${floor.height}`);
                alert(`Piano ${floor.level}: L'altezza deve essere maggiore di 0`);
                return false;
            }
            
            if (!floor.use) {
                console.log(`DEBUG: Piano ${floor.level} - destinazione d'uso mancante`);
                alert(`Piano ${floor.level}: Seleziona la destinazione d'uso`);
                return false;
            }
            
            console.log(`DEBUG: Piano ${floor.level} - validazione OK`);
        }
        
        // Verifica anno di costruzione
        console.log('DEBUG: Verificando anno di costruzione:', formData.building.construction_year);
        const currentYear = new Date().getFullYear();
        if (formData.building.construction_year < 1900 || formData.building.construction_year > currentYear) {
            console.log(`DEBUG: Anno di costruzione non valido: ${formData.building.construction_year}`);
            alert('Anno di costruzione non valido');
            return false;
        }
        
        console.log('DEBUG: Validazione completata con successo');
        return true;
        
    } catch (error) {
        console.error('ERRORE CRITICO in validateFormData:', error);
        console.error('ERRORE Stack trace:', error.stack);
        alert('Errore durante la validazione dei dati. Controlla la console.');
        return false;
    } finally {
        console.log('DEBUG: validateFormData - FINE');
    }
}

/**
 * Invia i dati al LLM per il calcolo
 * @param {Object} formData - Dati da inviare
 */
async function calculateWithLLM(formData) {
    console.log('DEBUG: calculateWithLLM - INIZIO');

    // Memorizza i dati di input per il report
    inertialMassState.lastInputData = { ...formData };
    console.log('DEBUG: Dati di input memorizzati per il report');

    // Mostra overlay di caricamento professionale
    showLoadingOverlay();
    
    // Disabilita il pulsante di calcolo
    const calculateBtn = document.getElementById('calculateBtn');
    const btnText = calculateBtn ? calculateBtn.querySelector('.btn-text') : null;
    const btnLoader = calculateBtn ? calculateBtn.querySelector('.btn-loader') : null;
    
    console.log('DEBUG: Pulsante calcola trovato:', calculateBtn ? 'SI' : 'NO');
    
    if (btnText && btnLoader) {
        btnText.style.display = 'none';
        btnLoader.style.display = 'inline-flex';
    }
    if (calculateBtn) {
        calculateBtn.disabled = true;
    }
    
    try {
        console.log('DEBUG: Invio dati al servizio LLM:', formData);
        
        // Aggiorna il messaggio di caricamento
        updateLoadingStep('Preparazione dati strutturali...', 0);
        await sleep(800);
        
        updateLoadingStep('Connessione al servizio di calcolo...', 1);
        await sleep(600);
        
        // URL dinamico basato sul percorso corrente
        const apiUrl = window.location.origin + '/progetti/asdp/inertial_mass/api/llm_service.php';
        console.log('DEBUG: URL API:', apiUrl);
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 180000); // 3 minuti timeout
        
        updateLoadingStep('Invio dati al motore di calcolo AI...', 2);
        await sleep(400);
        
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData),
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        console.log('DEBUG: Risposta fetch ricevuta, status:', response.status);
        
        updateLoadingStep('Elaborazione calcoli sismici...', 3);
        await sleep(1000);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('DEBUG: Errore risposta server:', errorText);
            throw new Error(`Errore server (${response.status}): ${errorText.substring(0, 200)}`);
        }
        
        const result = await response.json();
        console.log('DEBUG: Risposta dal servizio LLM:', result);
        
        if (result.error) {
            throw new Error(result.error);
        }
        
        updateLoadingStep('Generazione analisi strutturale...', 4);
        await sleep(800);
        
        updateLoadingStep('Finalizzazione risultati...', 5);
        await sleep(600);
        
        // Nascondi overlay di caricamento
        hideLoadingOverlay();
        
        // Mostra i risultati con animazione
        console.log('DEBUG: Chiamando displayResults');
        displayResults(result);
        
    } catch (error) {
        console.error('ERRORE CRITICO in calculateWithLLM:', error);
        console.error('ERRORE Stack trace:', error.stack);
        
        // Nascondi overlay di caricamento
        hideLoadingOverlay();
        
        // Gestione specifica per diversi tipi di errore
        let errorMessage = 'Si è verificato un errore durante il calcolo.';
        let errorType = 'Errore Generico';
        let errorIcon = '⚠️';
        
        if (error.name === 'AbortError') {
            errorType = 'Timeout di Calcolo';
            errorIcon = '⏱️';
            errorMessage = 'Il calcolo ha richiesto troppo tempo. Riprova con dati più semplici o verifica la connessione.';
        } else if (error.message.includes('Failed to fetch')) {
            errorType = 'Errore di Connessione';
            errorIcon = '🌐';
            errorMessage = 'Impossibile contattare il server. Verifica la connessione di rete.';
        } else if (error.message.includes('500')) {
            errorType = 'Errore Server';
            errorIcon = '🔧';
            errorMessage = 'Errore interno del server. Il problema è stato registrato nei log.';
        } else if (error.message) {
            errorMessage = error.message;
        }
        
        // Mostra l'errore in un elemento della UI professionale
        showErrorMessage(errorType, errorMessage, errorIcon, error);
        
    } finally {
        console.log('DEBUG: Ripristinando stato del pulsante');
        // Ripristina il bottone
        if (btnText && btnLoader) {
            btnText.style.display = 'inline';
            btnLoader.style.display = 'none';
        }
        if (calculateBtn) {
            calculateBtn.disabled = false;
        }
        inertialMassState.isCalculating = false;
        console.log('DEBUG: calculateWithLLM - FINE');
    }
}

/**
 * Mostra l'overlay di caricamento professionale
 */
function showLoadingOverlay() {
    // Rimuovi overlay esistente se presente
    const existingOverlay = document.querySelector('.loading-overlay');
    if (existingOverlay) {
        existingOverlay.remove();
    }
    
    const modalContainer = document.querySelector('.modal-container');
    if (!modalContainer) return;
    
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-title">Calcolo Massa Inerziale in Corso</div>
            <div class="loading-message">Il sistema sta elaborando i dati strutturali...</div>
            <div class="loading-progress">
                <div class="loading-progress-bar"></div>
            </div>
            <div class="loading-steps">
                <div class="loading-step" data-step="0">
                    <div class="loading-step-icon">1</div>
                    <span>Preparazione dati strutturali</span>
                </div>
                <div class="loading-step" data-step="1">
                    <div class="loading-step-icon">2</div>
                    <span>Connessione al servizio</span>
                </div>
                <div class="loading-step" data-step="2">
                    <div class="loading-step-icon">3</div>
                    <span>Invio dati al motore AI</span>
                </div>
                <div class="loading-step" data-step="3">
                    <div class="loading-step-icon">4</div>
                    <span>Elaborazione calcoli sismici</span>
                </div>
                <div class="loading-step" data-step="4">
                    <div class="loading-step-icon">5</div>
                    <span>Generazione analisi</span>
                </div>
                <div class="loading-step" data-step="5">
                    <div class="loading-step-icon">6</div>
                    <span>Finalizzazione risultati</span>
                </div>
            </div>
        </div>
    `;
    
    modalContainer.appendChild(loadingOverlay);
    
    // Animazione di entrata
    setTimeout(() => {
        loadingOverlay.style.opacity = '1';
    }, 10);
}

/**
 * Nascondi l'overlay di caricamento
 */
function hideLoadingOverlay() {
    const loadingOverlay = document.querySelector('.loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.opacity = '0';
        setTimeout(() => {
            loadingOverlay.remove();
        }, 300);
    }
}

/**
 * Aggiorna lo step di caricamento
 * @param {string} message - Messaggio da mostrare
 * @param {number} stepIndex - Indice dello step attivo
 */
function updateLoadingStep(message, stepIndex) {
    const loadingMessage = document.querySelector('.loading-message');
    const steps = document.querySelectorAll('.loading-step');
    
    if (loadingMessage) {
        loadingMessage.textContent = message;
    }
    
    steps.forEach((step, index) => {
        step.classList.remove('active', 'completed');
        if (index < stepIndex) {
            step.classList.add('completed');
            const icon = step.querySelector('.loading-step-icon');
            if (icon) icon.textContent = '✓';
        } else if (index === stepIndex) {
            step.classList.add('active');
        }
    });
}

/**
 * Mostra un messaggio di errore professionale
 * @param {string} errorType - Tipo di errore
 * @param {string} errorMessage - Messaggio di errore
 * @param {string} errorIcon - Icona dell'errore
 * @param {Error} error - Oggetto errore per dettagli tecnici
 */
function showErrorMessage(errorType, errorMessage, errorIcon, error) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message-professional';
    errorDiv.innerHTML = `
        <div style="
            background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
            border: 1px solid #dc2626;
            border-radius: 12px;
            padding: 2rem;
            margin: 1rem;
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
            color: #fef2f2;
        ">
            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                <div style="font-size: 2rem;">${errorIcon}</div>
                <div>
                    <h4 style="margin: 0; color: #fecaca; font-size: 1.3rem; font-weight: 600;">${errorType}</h4>
                    <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">${errorMessage}</p>
                </div>
            </div>
            
            <details style="margin-top: 1rem; cursor: pointer;">
                <summary style="font-weight: 500; color: #fecaca; margin-bottom: 0.5rem;">
                    Dettagli Tecnici
                </summary>
                <div style="
                    background-color: rgba(0, 0, 0, 0.3);
                    padding: 1rem;
                    border-radius: 6px;
                    font-family: monospace;
                    font-size: 0.9rem;
                    margin-top: 0.5rem;
                    border-left: 3px solid #dc2626;
                ">
                    <div><strong>Errore:</strong> ${error.name || 'Unknown'}</div>
                    <div><strong>Messaggio:</strong> ${error.message || 'Nessun messaggio'}</div>
                    <div><strong>Timestamp:</strong> ${new Date().toLocaleString()}</div>
                </div>
            </details>
            
            <div style="margin-top: 1.5rem; display: flex; gap: 1rem; justify-content: flex-end;">
                <button type="button" onclick="this.closest('.error-message-professional').remove()" style="
                    background-color: #dc2626;
                    color: white;
                    border: none;
                    padding: 0.75rem 1.5rem;
                    border-radius: 6px;
                    cursor: pointer;
                    font-weight: 500;
                    transition: background-color 0.2s ease;
                " onmouseover="this.style.backgroundColor='#b91c1c'" onmouseout="this.style.backgroundColor='#dc2626'">
                    Chiudi
                </button>
                <button type="button" onclick="location.reload()" style="
                    background-color: transparent;
                    color: #fecaca;
                    border: 1px solid #fecaca;
                    padding: 0.75rem 1.5rem;
                    border-radius: 6px;
                    cursor: pointer;
                    font-weight: 500;
                    transition: all 0.2s ease;
                " onmouseover="this.style.backgroundColor='rgba(254, 202, 202, 0.1)'" onmouseout="this.style.backgroundColor='transparent'">
                    Ricarica Pagina
                </button>
            </div>
        </div>
    `;
    
    // Inserisci l'errore nel modal
    const modalBody = document.querySelector('.modal-body');
    if (modalBody) {
        const existingError = modalBody.querySelector('.error-message-professional');
        if (existingError) {
            existingError.remove();
        }
        modalBody.appendChild(errorDiv);
        
        // Scroll verso l'errore
        errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

/**
 * Funzione di utilità per il delay
 * @param {number} ms - Millisecondi di attesa
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Visualizza i risultati del calcolo
 * @param {Object} results - Risultati dal LLM
 */
function displayResults(results) {
    console.log('DEBUG: displayResults - INIZIO');
    console.log('DEBUG: Risultati ricevuti:', results);

    // Memorizza i risultati per il report
    inertialMassState.lastCalculationResults = { ...results };
    console.log('DEBUG: Risultati memorizzati per il report');

    try {
        console.log('DEBUG: Nascondendo completamente il form');
        // Nascondi completamente il form per mostrare risultati a schermo intero
        const modalBody = document.querySelector('.modal-body');
        if (modalBody) {
            modalBody.style.display = 'none';
            console.log('DEBUG: Form nascosto completamente per risultati a schermo intero');
        } else {
            console.error('DEBUG: Elemento .modal-body non trovato');
        }
        
        console.log('DEBUG: Cercando sezione risultati');
        // Mostra la sezione risultati con animazione
        const resultsSection = document.getElementById('results-section');
        if (resultsSection) {
            console.log('DEBUG: Sezione risultati trovata, preparando visualizzazione');

            // Rimuovi lo style inline che nasconde la sezione
            resultsSection.removeAttribute('style');

            // Aggiungi la classe show per mostrare la sezione
            resultsSection.classList.add('show');

            // Prepara l'animazione
            resultsSection.style.opacity = '0';
            resultsSection.style.transform = 'translateY(30px)';
            resultsSection.style.transition = 'all 0.5s ease';
            
            console.log('DEBUG: Popolando HTML dei risultati');
            // Popola i risultati con contenuto migliorato
            const resultsContent = document.getElementById('results-content');
            if (resultsContent) {
                // IMPORTANTE: Pulisci il contenuto esistente prima di aggiungere nuovo HTML
                // per evitare duplicazioni
                resultsContent.innerHTML = '';
                console.log('DEBUG: Contenuto precedente pulito');
                
                // Genera e inserisci il nuovo HTML
                const newHTML = generateResultsHTML(results);
                resultsContent.innerHTML = newHTML;
                console.log('DEBUG: HTML dei risultati popolato con successo');
                
                // Applica stili per SCHERMO INTERO e anima l'entrata
                setTimeout(() => {
                    resultsSection.style.cssText = `
                        display: block !important;
                        opacity: 1 !important;
                        transform: translateY(0) !important;
                        transition: all 0.3s ease !important;
                        background-color: #1E1E1E !important;
                        color: #f8f9fa !important;
                        padding: 1.5rem !important;
                        border-radius: 0.5rem !important;
                        position: absolute !important;
                        top: 70px !important;
                        left: 0 !important;
                        right: 0 !important;
                        bottom: 0 !important;
                        height: calc(100% - 70px) !important;
                        max-height: none !important;
                        overflow-y: auto !important;
                        margin: 0 !important;
                        z-index: 10 !important;
                    `;
                    console.log('DEBUG: Risultati mostrati a SCHERMO INTERO');
                }, 400);
                
                // Anima gli elementi dei risultati in sequenza
                setTimeout(() => {
                    animateResultsElements();
                }, 700);
                
            } else {
                console.error('DEBUG: Elemento results-content non trovato');
            }
        } else {
            console.error('DEBUG: Elemento results-section non trovato');
            throw new Error('Sezione risultati non trovata nel DOM');
        }
        
    } catch (error) {
        console.error('ERRORE CRITICO in displayResults:', error);
        console.error('ERRORE Stack trace:', error.step);
        showErrorMessage('Errore Visualizzazione', 'Errore nella visualizzazione dei risultati.', '📊', error);
    } finally {
        console.log('DEBUG: displayResults - FINE');
    }
}

/**
 * Genera l'HTML professionale per i risultati
 * @param {Object} results - Risultati dal LLM
 * @returns {string} HTML formattato
 */
function generateResultsHTML(results) {
    console.log('DEBUG: generateResultsHTML - Generazione HTML risultati');
    
    const summaryHTML = `
        <div class="results-summary" style="opacity: 0; transform: translateY(20px);">
            <h4>📋 Riepilogo Calcolo</h4>
            <div class="result-item" data-animate="1">
                <span class="result-label">💪 Massa Totale:</span>
                <span class="result-value" data-value="${results.total_mass}">${results.total_mass.toFixed(2)} t</span>
            </div>
            <div class="result-item" data-animate="2">
                <span class="result-label">⏱️ Periodo Fondamentale:</span>
                <span class="result-value" data-value="${results.period}">${results.period.toFixed(3)} s</span>
            </div>
            <div class="result-item" data-animate="3">
                <span class="result-label">⚡ Forza Sismica Totale:</span>
                <span class="result-value" data-value="${results.total_force}">${results.total_force.toFixed(2)} kN</span>
            </div>
        </div>
    `;
    
    const detailsHTML = `
        <div class="results-details" style="opacity: 0; transform: translateY(20px);">
            <h4>Distribuzione Forze per Piano</h4>
            <div style="overflow-x: auto;">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>🏢 PIANO</th>
                            <th>⚖️ MASSA (T)</th>
                            <th>📏 ALTEZZA (M)</th>
                            <th>⭐ FORZA (KN)</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.floor_forces.map((floor, index) => `
                            <tr data-animate-row="${index}" style="opacity: 0; transform: translateX(-20px);">
                                <td><strong>${floor.level}</strong></td>
                                <td>${floor.mass.toFixed(2)}</td>
                                <td>${floor.height.toFixed(2)}</td>
                                <td><strong>${floor.force.toFixed(2)}</strong></td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    const analysisHTML = results.llm_analysis ? `
        <div class="results-analysis" style="opacity: 0; transform: translateY(20px);">
            <h4>🤖 Analisi AI</h4>
            <div class="analysis-content">${formatAnalysisContent(results.llm_analysis)}</div>
        </div>
    ` : '';

    // Sezione raccomandazioni dissipatori sismici
    const dampersHTML = results.damper_recommendations ? `
        <div class="results-dampers" style="opacity: 0; transform: translateY(20px);">
            <h4>🔧 Raccomandazioni Dissipatori Sismici</h4>
            ${generateDampersRecommendationHTML(results.damper_recommendations, results.total_mass)}
        </div>
    ` : '';

    console.log('DEBUG: generateResultsHTML - HTML generato correttamente');
    return summaryHTML + detailsHTML + dampersHTML + analysisHTML;
}

/**
 * Genera l'HTML per le raccomandazioni dei dissipatori sismici
 * @param {Object} damperRecommendations - Raccomandazioni dai calcoli
 * @param {number} totalMass - Massa inerziale totale
 * @returns {string} HTML formattato
 */
function generateDampersRecommendationHTML(damperRecommendations, totalMass) {
    console.log('DEBUG: generateDampersRecommendationHTML - Generazione HTML dissipatori');

    const { optimal_combination, technical_explanation, building_analysis, required_dissipation } = damperRecommendations;

    // Sezione riepilogo raccomandazione
    const summarySection = `
        <div class="dampers-summary" style="margin-bottom: 2rem;">
            <div class="damper-summary-grid">
                <div class="damper-summary-item">
                    <span class="damper-label">🏗️ Massa Inerziale:</span>
                    <span class="damper-value">${totalMass.toFixed(2)} t</span>
                </div>
                <div class="damper-summary-item">
                    <span class="damper-label">⚡ Dissipazione Richiesta:</span>
                    <span class="damper-value">${required_dissipation.toFixed(0)} kN</span>
                </div>
                <div class="damper-summary-item">
                    <span class="damper-label">🎯 Efficienza Raggiunta:</span>
                    <span class="damper-value">${optimal_combination.efficiency_ratio}%</span>
                </div>
                <div class="damper-summary-item">
                    <span class="damper-label">📊 Totale Dissipatori:</span>
                    <span class="damper-value">${optimal_combination.total_dampers} unità</span>
                </div>
            </div>
        </div>
    `;

    // Tabella raccomandazioni specifiche
    const recommendationTable = `
        <div class="dampers-recommendation" style="margin-bottom: 2rem;">
            <h5>🎯 Raccomandazione Ottimale</h5>
            <div style="overflow-x: auto;">
                <table class="dampers-table">
                    <thead>
                        <tr>
                            <th>🔧 TIPOLOGIA</th>
                            <th>⚡ CAPACITÀ UNITARIA</th>
                            <th>📊 QUANTITÀ</th>
                            <th>💪 CAPACITÀ TOTALE</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${optimal_combination.dampers.map((damper, index) => `
                            <tr data-animate-damper="${index}" style="opacity: 0; transform: translateX(-20px);">
                                <td><strong>Categoria ${damper.type}</strong></td>
                                <td>${damper.capacity_each} kN</td>
                                <td><strong>${damper.quantity}x</strong></td>
                                <td><strong>${damper.total_capacity} kN</strong></td>
                            </tr>
                        `).join('')}
                        <tr class="dampers-total-row" style="opacity: 0; transform: translateX(-20px);">
                            <td colspan="3"><strong>🎯 TOTALE SISTEMA</strong></td>
                            <td><strong>${optimal_combination.total_capacity} kN</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    `;

    // Spiegazione tecnica
    const technicalSection = `
        <div class="dampers-technical" style="margin-bottom: 2rem;">
            <h5>📋 Analisi Tecnica</h5>
            <div class="technical-content">${formatAnalysisContent(technical_explanation)}</div>
        </div>
    `;

    // Note specifiche per l'edificio
    const buildingNotesSection = building_analysis && building_analysis.length > 0 ? `
        <div class="dampers-building-notes">
            <h5>🏢 Note Specifiche per l'Edificio</h5>
            <div class="building-notes-list">
                ${building_analysis.map(note => `
                    <div class="building-note">
                        <span class="note-icon">💡</span>
                        <span class="note-text">${note}</span>
                    </div>
                `).join('')}
            </div>
        </div>
    ` : '';

    return summarySection + recommendationTable + technicalSection + buildingNotesSection;
}

/**
 * Formatta il contenuto dell'analisi AI per una migliore leggibilità
 * @param {string} analysis - Testo dell'analisi
 * @returns {string} Testo formattato
 */
function formatAnalysisContent(analysis) {
    // Sostituisce i punti elenco con emoji
    let formatted = analysis
        .replace(/\n- /g, '\n🔸 ')
        .replace(/\n\* /g, '\n🔹 ')
        .replace(/\n\d+\. /g, '\n📌 ');
    
    // Evidenzia parole chiave importanti
    const keywords = [
        'vulnerabilità sismica', 'zona sismica', 'categoria di sottosuolo',
        'periodo fondamentale', 'forza sismica', 'massa inerziale',
        'normativa', 'NTC', 'Eurocodice', 'raccomandazioni'
    ];
    
    keywords.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
        formatted = formatted.replace(regex, `<strong style="color: #4ade80;">$&</strong>`);
    });
    
    return formatted;
}

/**
 * Anima gli elementi dei risultati in sequenza
 */
function animateResultsElements() {
    // Anima il riepilogo
    const summary = document.querySelector('.results-summary');
    if (summary) {
        summary.style.transition = 'all 0.6s ease';
        summary.style.opacity = '1';
        summary.style.transform = 'translateY(0)';
        
        // Anima gli elementi del riepilogo
        const resultItems = summary.querySelectorAll('.result-item');
        resultItems.forEach((item, index) => {
            setTimeout(() => {
                item.style.transition = 'all 0.4s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
                
                // Effetto contatore per i valori numerici
                const valueElement = item.querySelector('.result-value');
                if (valueElement && valueElement.dataset.value) {
                    animateCounter(valueElement, parseFloat(valueElement.dataset.value));
                }
            }, index * 200);
        });
    }
    
    // Anima la tabella dettagli
    setTimeout(() => {
        const details = document.querySelector('.results-details');
        if (details) {
            details.style.transition = 'all 0.6s ease';
            details.style.opacity = '1';
            details.style.transform = 'translateY(0)';
            
            // Anima le righe della tabella
            const rows = details.querySelectorAll('[data-animate-row]');
            rows.forEach((row, index) => {
                setTimeout(() => {
                    row.style.transition = 'all 0.4s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateX(0)';
                }, index * 100);
            });
        }
    }, 800);
    
    // Anima la sezione dissipatori
    setTimeout(() => {
        const dampers = document.querySelector('.results-dampers');
        if (dampers) {
            dampers.style.transition = 'all 0.6s ease';
            dampers.style.opacity = '1';
            dampers.style.transform = 'translateY(0)';

            // Anima le righe della tabella dissipatori
            const damperRows = dampers.querySelectorAll('[data-animate-damper]');
            damperRows.forEach((row, index) => {
                setTimeout(() => {
                    row.style.transition = 'all 0.4s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateX(0)';
                }, index * 150);
            });

            // Anima la riga totale
            setTimeout(() => {
                const totalRow = dampers.querySelector('.dampers-total-row');
                if (totalRow) {
                    totalRow.style.transition = 'all 0.4s ease';
                    totalRow.style.opacity = '1';
                    totalRow.style.transform = 'translateX(0)';
                }
            }, damperRows.length * 150 + 200);
        }
    }, 1400);

    // Anima l'analisi AI
    setTimeout(() => {
        const analysis = document.querySelector('.results-analysis');
        if (analysis) {
            analysis.style.transition = 'all 0.6s ease';
            analysis.style.opacity = '1';
            analysis.style.transform = 'translateY(0)';
        }
    }, 2000);
}

/**
 * Anima un contatore numerico
 * @param {HTMLElement} element - Elemento da animare
 * @param {number} targetValue - Valore target
 */
function animateCounter(element, targetValue) {
    const duration = 1500; // 1.5 secondi
    const startTime = performance.now();
    const startValue = 0;
    
    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function (ease-out)
        const easedProgress = 1 - Math.pow(1 - progress, 3);
        
        const currentValue = startValue + (targetValue - startValue) * easedProgress;
        
        // Determina il numero di decimali basato sul valore target
        let decimals = 0;
        if (targetValue < 10) decimals = 3;
        else if (targetValue < 100) decimals = 2;
        else decimals = 0;
        
        // Aggiorna il testo mantenendo l'unità di misura
        const originalText = element.textContent;
        const unit = originalText.match(/[a-zA-Z]+$/)?.[0] || '';
        element.textContent = `${currentValue.toFixed(decimals)} ${unit}`;
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        }
    }
    
    requestAnimationFrame(updateCounter);
}

/**
 * Reset del calcolo per nuovo inserimento
 */
function resetCalculation() {
    console.log('DEBUG: resetCalculation - INIZIO');

    // Mostra il form con animazione
    const modalBody = document.querySelector('.modal-body');
    if (modalBody) {
        modalBody.style.display = 'block';
        modalBody.style.opacity = '1';
        modalBody.style.transform = 'translateY(0)';
    }

    // Nascondi i risultati
    const resultsSection = document.getElementById('results-section');
    if (resultsSection) {
        resultsSection.classList.remove('show');
        resultsSection.style.display = 'none';
    }

    // Reset form
    const form = document.getElementById('inertialMassForm');
    if (form) {
        form.reset();
    }

    // Reset tipologie costruttive
    resetConstructionTypeFields();

    // Reset piani (mantieni solo il primo)
    inertialMassState.floors = [];
    const floorsContainer = document.getElementById('floors-container');
    if (floorsContainer) {
        floorsContainer.innerHTML = '';
    }
    addFloor();

    console.log('DEBUG: resetCalculation - FINE');
}

/**
 * Salva i risultati e genera report completo
 */
async function saveResults() {
    console.log('DEBUG: saveResults - INIZIO');

    try {
        // Verifica che ci siano risultati da salvare
        if (!inertialMassState.lastCalculationResults) {
            throw new Error('Nessun risultato disponibile per il salvataggio');
        }

        console.log('DEBUG: Generando report completo...');

        // Genera e apri il report in una nuova finestra
        const reportWindow = await generateAndOpenReport();

        // Salva anche nel database (logica esistente)
        console.log('DEBUG: Salvando nel database...');
        await saveToDatabase();

        console.log('DEBUG: Salvataggio completato con successo');

        // Mostra messaggio di successo
        showSuccessMessage('Report generato e risultati salvati con successo!');

    } catch (error) {
        console.error('ERRORE CRITICO in saveResults:', error);
        console.error('ERRORE Stack trace:', error.stack);
        showErrorMessage('Errore Salvataggio', 'Si è verificato un errore durante il salvataggio: ' + error.message);
    }
}

/**
 * Genera e apre il report completo in una nuova finestra
 */
async function generateAndOpenReport() {
    console.log('DEBUG: generateAndOpenReport - INIZIO');

    try {
        // Carica il template HTML
        const templateResponse = await fetch('/progetti/asdp/inertial_mass/report_template.html');
        if (!templateResponse.ok) {
            throw new Error('Impossibile caricare il template del report');
        }

        let templateHTML = await templateResponse.text();
        console.log('DEBUG: Template caricato correttamente');

        // Popola il template con i dati
        const populatedHTML = populateReportTemplate(templateHTML);
        console.log('DEBUG: Template popolato con i dati');

        // Apri il report in una nuova finestra
        const reportWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        if (!reportWindow) {
            throw new Error('Impossibile aprire la finestra del report. Verifica che i popup non siano bloccati.');
        }

        // Scrivi il contenuto nella nuova finestra
        reportWindow.document.write(populatedHTML);
        reportWindow.document.close();

        // Imposta il titolo della finestra
        reportWindow.document.title = 'Report Analisi Massa Inerziale - ASDP v2.5.0';

        console.log('DEBUG: Report aperto in nuova finestra');
        return reportWindow;

    } catch (error) {
        console.error('ERRORE in generateAndOpenReport:', error);
        throw error;
    }
}

/**
 * Salva i risultati nel database (logica esistente)
 */
async function saveToDatabase() {
    console.log('DEBUG: saveToDatabase - INIZIO');

    try {
        const response = await fetch('/progetti/asdp/inertial_mass/api/save_results.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                calculation_id: inertialMassState.currentCalculationId,
                project_id: window.currentProjectId || null,
                results_data: inertialMassState.lastCalculationResults
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.error || 'Errore nel salvataggio nel database');
        }

        console.log('DEBUG: Salvataggio database completato');
        return result;

    } catch (error) {
        console.error('ERRORE in saveToDatabase:', error);
        // Non bloccare il processo se il salvataggio DB fallisce
        console.warn('ATTENZIONE: Salvataggio database fallito, ma report generato correttamente');
        return null;
    }
}

/**
 * Popola il template HTML con i dati del calcolo
 */
function populateReportTemplate(templateHTML) {
    console.log('DEBUG: populateReportTemplate - INIZIO');

    const results = inertialMassState.lastCalculationResults;
    const inputData = inertialMassState.lastInputData || {};

    // Genera dati spettri di risposta per il report
    const spectrumData = generateSpectrumDataForReport(inputData, results);
    console.log('DEBUG: Dati spettri generati per report:', spectrumData);

    // Debug: verifica struttura dati
    console.log('DEBUG: inputData completo:', inputData);
    console.log('DEBUG: inputData.location:', inputData.location);
    console.log('DEBUG: inputData.seismic_params:', inputData.seismic_params);
    console.log('DEBUG: inputData.building:', inputData.building);

    // Timestamp
    const now = new Date();
    const timestamp = now.toLocaleDateString('it-IT');
    const fullTimestamp = now.toLocaleString('it-IT');

    // Dati di base
    let populatedHTML = templateHTML
        .replace(/{{TIMESTAMP}}/g, timestamp)
        .replace(/{{FULL_TIMESTAMP}}/g, fullTimestamp)
        .replace(/{{CALCULATION_ID}}/g, results.calculation_id || 'N/A');

    // Dati di input - correzione mapping struttura dati
    populatedHTML = populatedHTML
        .replace(/{{COORDINATES}}/g, formatCoordinates(inputData))
        .replace(/{{SEISMIC_ZONE}}/g, inputData.seismic_params?.zone || 'N/A')
        .replace(/{{AG}}/g, inputData.seismic_params?.ag || 'N/A')
        .replace(/{{F0}}/g, inputData.seismic_params?.F0 || 'N/A')
        .replace(/{{TC}}/g, inputData.seismic_params?.TC || 'N/A')
        .replace(/{{SOIL_CATEGORY}}/g, inputData.seismic_params?.soil_category || 'N/A')
        .replace(/{{CONSTRUCTION_CATEGORY}}/g, formatConstructionCategory(inputData.building?.construction_category))
        .replace(/{{STRUCTURE_TYPE}}/g, formatStructureType(inputData.building?.structure_type))
        .replace(/{{SLAB_TYPE}}/g, formatSlabType(inputData.building?.slab_type))
        .replace(/{{CONSTRUCTION_YEAR}}/g, inputData.building?.construction_year || 'N/A');

    // Risultati calcolo massa inerziale
    populatedHTML = populatedHTML
        .replace(/{{TOTAL_MASS}}/g, results.total_mass || 'N/A')
        .replace(/{{PERIOD}}/g, results.period || 'N/A')
        .replace(/{{TOTAL_FORCE}}/g, results.total_force || 'N/A')
        .replace(/{{RESPONSE_SPECTRUM}}/g, results.response_spectrum || 'N/A');

    // Tabella forze per piano
    const floorForcesTable = generateFloorForcesTableHTML(results.floor_forces || []);
    populatedHTML = populatedHTML.replace(/{{FLOOR_FORCES_TABLE}}/g, floorForcesTable);

    // Raccomandazioni dissipatori
    if (results.damper_recommendations) {
        const damperData = results.damper_recommendations;
        populatedHTML = populatedHTML
            .replace(/{{REQUIRED_DISSIPATION}}/g, damperData.required_dissipation || 'N/A')
            .replace(/{{EFFICIENCY_RATIO}}/g, damperData.optimal_combination?.efficiency_ratio || 'N/A')
            .replace(/{{TOTAL_DAMPERS}}/g, damperData.optimal_combination?.total_dampers || 'N/A');

        // Tabella dissipatori
        const dampersTable = generateDampersTableHTML(damperData.optimal_combination?.dampers || []);
        populatedHTML = populatedHTML.replace(/{{DAMPERS_TABLE}}/g, dampersTable);

        // Analisi tecnica
        const technicalExplanation = damperData.technical_explanation || 'Analisi tecnica non disponibile';
        populatedHTML = populatedHTML.replace(/{{TECHNICAL_EXPLANATION}}/g, technicalExplanation);

        // Note specifiche edificio
        const buildingNotes = generateBuildingNotesHTML(damperData.building_analysis || []);
        populatedHTML = populatedHTML.replace(/{{BUILDING_NOTES}}/g, buildingNotes);
    } else {
        // Nessuna raccomandazione dissipatori disponibile
        populatedHTML = populatedHTML
            .replace(/{{REQUIRED_DISSIPATION}}/g, 'N/A')
            .replace(/{{EFFICIENCY_RATIO}}/g, 'N/A')
            .replace(/{{TOTAL_DAMPERS}}/g, 'N/A')
            .replace(/{{DAMPERS_TABLE}}/g, '<tr><td colspan="4">Raccomandazioni dissipatori non disponibili</td></tr>')
            .replace(/{{TECHNICAL_EXPLANATION}}/g, 'Analisi tecnica non disponibile')
            .replace(/{{BUILDING_NOTES}}/g, '<div class="building-note"><span class="note-icon">ℹ️</span><span class="note-text">Raccomandazioni non disponibili per questo calcolo</span></div>');
    }

    // Sezione analisi AI
    const aiAnalysisSection = generateAIAnalysisSection(results.llm_analysis);
    populatedHTML = populatedHTML.replace(/{{AI_ANALYSIS_SECTION}}/g, aiAnalysisSection);

    // Sostituzione placeholder spettri di risposta
    populatedHTML = populatedHTML
        .replace(/{{SPECTRUM_DATA_BEFORE}}/g, JSON.stringify(spectrumData.spectrum_data_before))
        .replace(/{{SPECTRUM_DATA_AFTER}}/g, JSON.stringify(spectrumData.spectrum_data_after))
        .replace(/{{DAMPING_BEFORE}}/g, spectrumData.damping_before)
        .replace(/{{DAMPING_AFTER}}/g, spectrumData.damping_after)
        .replace(/{{SS_COEFF}}/g, spectrumData.coefficients.SS)
        .replace(/{{CC_COEFF}}/g, spectrumData.coefficients.CC)
        .replace(/{{S_COEFF}}/g, spectrumData.coefficients.S)
        .replace(/{{TB_PERIOD}}/g, spectrumData.periods.TB)
        .replace(/{{TC_PERIOD}}/g, spectrumData.periods.TC)
        .replace(/{{TD_PERIOD}}/g, spectrumData.periods.TD)
        .replace(/{{SE_MAX_BEFORE}}/g, spectrumData.max_values.before)
        .replace(/{{SE_MAX_AFTER}}/g, spectrumData.max_values.after)
        .replace(/{{REDUCTION_AVERAGE}}/g, spectrumData.reduction_analysis.average)
        .replace(/{{REDUCTION_MAX}}/g, spectrumData.reduction_analysis.max)
        .replace(/{{REDUCTION_T1}}/g, spectrumData.reduction_analysis.t1)
        .replace(/{{DAMPER_EFFICIENCY}}/g, spectrumData.reduction_analysis.efficiency);

    console.log('DEBUG: Template popolato completamente con dati spettri');
    return populatedHTML;
}

/**
 * Formatta le coordinate per il report
 */
function formatCoordinates(inputData) {
    if (inputData.location?.lat && inputData.location?.lon) {
        return `${inputData.location.lat}, ${inputData.location.lon}`;
    }
    return 'N/A';
}

/**
 * Formatta la categoria costruttiva
 */
function formatConstructionCategory(category) {
    const categories = {
        'building': 'Edificio Generico',
        'bridge': 'Ponte/Viadotto',
        'prefab_building': 'Edificio Prefabbricato'
    };
    return categories[category] || category || 'N/A';
}

/**
 * Formatta la tipologia strutturale
 */
function formatStructureType(type) {
    const types = {
        'concrete': 'Cemento Armato',
        'steel': 'Acciaio',
        'masonry': 'Muratura',
        'wood': 'Legno',
        'mixed': 'Mista',
        'prestressed_concrete': 'Cemento Armato Precompresso'
    };
    return types[type] || type || 'N/A';
}

/**
 * Formatta la tipologia di solaio
 */
function formatSlabType(type) {
    const types = {
        'hollow_brick': 'Laterocemento',
        'solid_slab': 'Soletta Piena',
        'steel_deck': 'Lamiera Grecata',
        'wood_slab': 'Legno',
        'prefab': 'Prefabbricato',
        'prestressed_deck': 'Impalcato Precompresso',
        'composite_deck': 'Impalcato Misto',
        'concrete_deck': 'Impalcato in C.A.',
        'prestressed_slab': 'Solaio Precompresso',
        'prefab_panels': 'Pannelli Prefabbricati',
        'hollow_core': 'Lastre Alveolari'
    };
    return types[type] || type || 'N/A';
}

/**
 * Genera la tabella HTML delle forze per piano
 */
function generateFloorForcesTableHTML(floorForces) {
    if (!floorForces || floorForces.length === 0) {
        return '<tr><td colspan="5">Nessun dato disponibile</td></tr>';
    }

    return floorForces.map((floor, index) => {
        const floorNumber = index + 1;
        const mass = floor.mass ? floor.mass.toFixed(2) : 'N/A';
        const height = floor.height ? floor.height.toFixed(2) : 'N/A';
        const use = formatFloorUse(floor.use);
        const force = floor.force ? floor.force.toFixed(2) : 'N/A';

        return `
            <tr>
                <td><strong>Piano ${floorNumber}</strong></td>
                <td>${mass}</td>
                <td>${height}</td>
                <td>${use}</td>
                <td><strong>${force}</strong></td>
            </tr>
        `;
    }).join('');
}

/**
 * Genera la tabella HTML dei dissipatori raccomandati
 */
function generateDampersTableHTML(dampers) {
    if (!dampers || dampers.length === 0) {
        return '<tr><td colspan="4">Nessuna raccomandazione disponibile</td></tr>';
    }

    let tableHTML = '';
    let totalCapacity = 0;

    dampers.forEach(damper => {
        tableHTML += `
            <tr>
                <td><strong>Categoria ${damper.type}</strong></td>
                <td>${damper.capacity_each} kN</td>
                <td><strong>${damper.quantity}x</strong></td>
                <td><strong>${damper.total_capacity} kN</strong></td>
            </tr>
        `;
        totalCapacity += damper.total_capacity;
    });

    // Riga totale
    tableHTML += `
        <tr style="background: rgba(72, 187, 120, 0.2); border-top: 2px solid #48bb78;">
            <td colspan="3"><strong>🎯 TOTALE SISTEMA</strong></td>
            <td><strong style="color: #48bb78;">${totalCapacity} kN</strong></td>
        </tr>
    `;

    return tableHTML;
}

/**
 * Genera l'HTML delle note specifiche per l'edificio
 */
function generateBuildingNotesHTML(buildingNotes) {
    if (!buildingNotes || buildingNotes.length === 0) {
        return '<div class="building-note"><span class="note-icon">ℹ️</span><span class="note-text">Nessuna nota specifica disponibile</span></div>';
    }

    return buildingNotes.map(note => `
        <div class="building-note">
            <span class="note-icon">💡</span>
            <span class="note-text">${note}</span>
        </div>
    `).join('');
}

/**
 * Genera i dati degli spettri di risposta per il report
 */
function generateSpectrumDataForReport(inputData, results) {
    console.log('DEBUG: Generando dati spettri per report...');

    try {
        // Estrai parametri sismici dai dati di input
        const seismicData = inertialMassState.seismicData || {};

        // Parametri base per calcoli spettri
        const baseParams = {
            ag: seismicData.ag || 0.062,
            F0: seismicData.F0 || 2.604,
            TC: seismicData.TC || 0.268,
            soil_category: seismicData.soil_category || 'C',
            topographic_category: 'T1'
        };

        // Calcolo smorzamento strutturale base
        const dampingBefore = calculateStructuralDamping(inputData);

        // Calcolo smorzamento equivalente con dissipatori
        const dampingAfter = calculateEquivalentDamping(dampingBefore, results);

        // Genera spettri sintetici per il report
        const spectrumBefore = generateSyntheticSpectrum(baseParams, dampingBefore);
        const spectrumAfter = generateSyntheticSpectrum(baseParams, dampingAfter);

        // Calcola analisi riduzione
        const reductionAnalysis = calculateSpectrumReduction(spectrumBefore, spectrumAfter);

        // Calcola metadati dinamici per entrambi gli spettri
        const metadataBefore = calculateSpectrumMetadata(baseParams, dampingBefore);
        const metadataAfter = calculateSpectrumMetadata(baseParams, dampingAfter);

        console.log('DEBUG: Metadati ANTE:', metadataBefore);
        console.log('DEBUG: Metadati POST:', metadataAfter);
        console.log('DEBUG: Smorzamento ANTE:', dampingBefore);
        console.log('DEBUG: Smorzamento POST:', dampingAfter);
        console.log('DEBUG: Riduzione analisi:', reductionAnalysis);

        return {
            spectrum_data_before: formatSpectrumForChart(spectrumBefore),
            spectrum_data_after: formatSpectrumForChart(spectrumAfter),
            damping_before: Math.round(dampingBefore * 10) / 10,
            damping_after: Math.round(dampingAfter * 10) / 10,
            coefficients: calculateAmplificationCoefficients(baseParams),
            periods: calculateCharacteristicPeriods(baseParams),
            reduction_analysis: reductionAnalysis,
            max_values: {
                before: Math.max(...spectrumBefore.map(p => p.Se)),
                after: Math.max(...spectrumAfter.map(p => p.Se))
            },
            metadata_before: metadataBefore,
            metadata_after: metadataAfter
        };

    } catch (error) {
        console.error('Errore generazione dati spettri:', error);
        return getDefaultSpectrumData();
    }
}

/**
 * Calcola lo smorzamento strutturale base
 */
function calculateStructuralDamping(inputData) {
    let baseDamping = 5.0; // Default per c.a.

    // Fattore per anno di costruzione
    const year = inputData.construction_year || 2000;
    if (year < 1980) baseDamping = 4.0;
    else if (year > 2008) baseDamping = 5.5;

    // Fattore per numero di piani
    const floors = inputData.floors ? inputData.floors.length : 3;
    if (floors > 5) baseDamping += 0.5;
    else if (floors < 3) baseDamping -= 0.5;

    return Math.max(3.0, Math.min(7.0, baseDamping));
}

/**
 * Calcola lo smorzamento equivalente con dissipatori
 */
function calculateEquivalentDamping(structuralDamping, results) {
    console.log('DEBUG: calculateEquivalentDamping - INIZIO');
    console.log('DEBUG: Smorzamento strutturale base:', structuralDamping);
    console.log('DEBUG: Risultati completi:', results);

    let additionalDamping = 0;

    // Estrai dati dissipatori dai risultati - prova diversi percorsi
    let dampers = null;

    if (results.damper_recommendations && results.damper_recommendations.recommended_dampers) {
        dampers = results.damper_recommendations.recommended_dampers;
        console.log('DEBUG: Dissipatori trovati in damper_recommendations.recommended_dampers');
    } else if (results.damper_recommendations && Array.isArray(results.damper_recommendations)) {
        dampers = results.damper_recommendations;
        console.log('DEBUG: Dissipatori trovati in damper_recommendations (array)');
    } else if (results.recommended_dampers) {
        dampers = results.recommended_dampers;
        console.log('DEBUG: Dissipatori trovati in recommended_dampers');
    }

    console.log('DEBUG: Dissipatori estratti:', dampers);

    if (dampers && Array.isArray(dampers)) {
        dampers.forEach((damper, index) => {
            console.log(`DEBUG: Dissipatore ${index}:`, damper);

            // Prova diversi nomi di proprietà
            const capacity = damper.capacity_each || damper.capacity || damper.total_capacity || 1000;
            const quantity = damper.quantity || 1;

            console.log(`DEBUG: Capacità: ${capacity} kN, Quantità: ${quantity}`);

            // Formula empirica migliorata: ogni 500 KN aggiunge ~3% di smorzamento
            const damperContribution = (capacity / 500) * 3.0 * quantity;
            additionalDamping += damperContribution;

            console.log(`DEBUG: Contributo dissipatore: ${damperContribution}%`);
        });
    } else {
        console.log('DEBUG: Nessun dissipatore trovato, usando valori di default');
        // Se non ci sono dissipatori, aggiungi comunque un po' di smorzamento per test
        additionalDamping = 8.0; // Default per test
    }

    console.log('DEBUG: Smorzamento aggiuntivo totale:', additionalDamping);

    // Smorzamento equivalente con fattore di efficienza
    const equivalentDamping = structuralDamping + additionalDamping * 0.8; // Aumentato fattore efficienza

    console.log('DEBUG: Smorzamento equivalente calcolato:', equivalentDamping);

    const finalDamping = Math.min(20.0, Math.max(structuralDamping + 2.0, equivalentDamping));
    console.log('DEBUG: Smorzamento finale (con limiti):', finalDamping);

    return finalDamping;
}

/**
 * Genera spettro sintetico per visualizzazione
 */
function generateSyntheticSpectrum(params, damping) {
    const spectrum = [];
    const periods = [];

    // Genera periodi da 0.01 a 4.0 secondi
    for (let i = 0; i <= 100; i++) {
        periods.push(0.01 + (i * 0.04));
    }

    // Calcola coefficienti
    const coeffs = calculateAmplificationCoefficients(params);
    const charPeriods = calculateCharacteristicPeriods(params);
    const eta = Math.sqrt(10 / (5 + damping));

    periods.forEach(T => {
        let Se;

        if (T < charPeriods.TB) {
            Se = params.ag * coeffs.S * eta * params.F0 * ((T / charPeriods.TB) + (1 / (eta * params.F0)) * (1 - T / charPeriods.TB));
        } else if (T < charPeriods.TC) {
            Se = params.ag * coeffs.S * eta * params.F0;
        } else if (T < charPeriods.TD) {
            Se = params.ag * coeffs.S * eta * params.F0 * (charPeriods.TC / T);
        } else {
            Se = params.ag * coeffs.S * eta * params.F0 * (charPeriods.TC * charPeriods.TD) / (T * T);
        }

        spectrum.push({ T: T, Se: Math.max(0, Se) });
    });

    return spectrum;
}

/**
 * Calcola coefficienti di amplificazione
 */
function calculateAmplificationCoefficients(params) {
    // Semplificazione per categoria C
    return {
        SS: 1.15,
        CC: 1.05,
        ST: 1.0,
        S: 1.15
    };
}

/**
 * Calcola periodi caratteristici
 */
function calculateCharacteristicPeriods(params) {
    const TC = 1.05 * params.TC; // CC * TC*
    return {
        TB: TC / 3,
        TC: TC,
        TD: 4.0 * params.ag + 1.6
    };
}

/**
 * Formatta spettro per Chart.js con precisione 4 decimali
 */
function formatSpectrumForChart(spectrum) {
    return {
        periods: spectrum.map(p => parseFloat(p.T.toFixed(4))),
        accelerations: spectrum.map(p => parseFloat(p.Se.toFixed(4)))
    };
}

/**
 * Calcola analisi riduzione tra spettri
 */
function calculateSpectrumReduction(spectrumBefore, spectrumAfter) {
    console.log('DEBUG: calculateSpectrumReduction - INIZIO');
    console.log('DEBUG: Punti spettro ANTE:', spectrumBefore.length);
    console.log('DEBUG: Punti spettro POST:', spectrumAfter.length);

    let totalReduction = 0;
    let maxReduction = 0;
    let count = 0;
    let reductions = [];

    for (let i = 0; i < Math.min(spectrumBefore.length, spectrumAfter.length); i++) {
        const before = spectrumBefore[i].Se;
        const after = spectrumAfter[i].Se;

        if (before > 0.001) { // Soglia minima per evitare divisioni per zero
            const reduction = ((before - after) / before) * 100;
            reductions.push(reduction);
            totalReduction += reduction;
            maxReduction = Math.max(maxReduction, reduction);
            count++;

            // Debug per i primi 5 punti
            if (i < 5) {
                console.log(`DEBUG: Punto ${i}: T=${spectrumBefore[i].T}s, ANTE=${before.toFixed(6)}g, POST=${after.toFixed(6)}g, Riduzione=${reduction.toFixed(2)}%`);
            }
        }
    }

    const averageReduction = count > 0 ? totalReduction / count : 0;

    // Calcola riduzione al periodo fondamentale (circa T=0.5s per edifici tipici)
    let reductionT1 = averageReduction;
    const t1Index = spectrumBefore.findIndex(p => p.T >= 0.5);
    if (t1Index >= 0 && t1Index < reductions.length) {
        reductionT1 = reductions[t1Index];
    }

    // Efficienza dissipatori basata su riduzione media
    const efficiency = Math.min(100, Math.max(0, averageReduction * 1.5));

    console.log('DEBUG: Riduzione media calcolata:', averageReduction);
    console.log('DEBUG: Riduzione massima:', maxReduction);
    console.log('DEBUG: Riduzione T1:', reductionT1);
    console.log('DEBUG: Efficienza dissipatori:', efficiency);

    return {
        average: parseFloat(averageReduction.toFixed(4)),
        max: parseFloat(maxReduction.toFixed(4)),
        t1: parseFloat(reductionT1.toFixed(4)),
        efficiency: parseFloat(efficiency.toFixed(4))
    };
}

/**
 * Calcola metadati spettro dinamici
 */
function calculateSpectrumMetadata(params, damping) {
    const coeffs = calculateAmplificationCoefficients(params);
    const periods = calculateCharacteristicPeriods(params);
    const eta = Math.sqrt(10 / (5 + damping));
    const Se_max = params.ag * coeffs.S * eta * params.F0;

    return {
        SS: parseFloat(coeffs.SS.toFixed(4)),
        CC: parseFloat(coeffs.CC.toFixed(4)),
        S: parseFloat(coeffs.S.toFixed(4)),
        TB: parseFloat(periods.TB.toFixed(4)),
        TC: parseFloat(periods.TC.toFixed(4)),
        TD: parseFloat(periods.TD.toFixed(4)),
        Se_max: parseFloat(Se_max.toFixed(4))
    };
}

/**
 * Dati di default in caso di errore
 */
function getDefaultSpectrumData() {
    return {
        spectrum_data_before: { periods: [], accelerations: [] },
        spectrum_data_after: { periods: [], accelerations: [] },
        damping_before: 5.0,
        damping_after: 12.0,
        coefficients: { SS: 1.15, CC: 1.05, S: 1.15 },
        periods: { TB: 0.089, TC: 0.268, TD: 1.848 },
        reduction_analysis: { average: 0, max: 0, t1: 0, efficiency: 0 },
        max_values: { before: 0, after: 0 }
    };
}

/**
 * Genera la sezione analisi AI
 */
function generateAIAnalysisSection(aiAnalysis) {
    if (!aiAnalysis) {
        return '<!-- Sezione analisi AI non disponibile -->';
    }

    return `
        <div class="section">
            <h2><i class="fas fa-robot"></i> Analisi AI Avanzata</h2>
            <div class="technical-analysis">${formatAnalysisContent(aiAnalysis)}</div>
        </div>
    `;
}

/**
 * Formatta la destinazione d'uso del piano
 */
function formatFloorUse(use) {
    const uses = {
        'residential': 'Residenziale',
        'office': 'Uffici',
        'commercial': 'Commerciale',
        'industrial': 'Industriale',
        'storage': 'Magazzino',
        'highway': 'Autostrada',
        'urban_road': 'Strada Urbana',
        'railway': 'Ferrovia',
        'pedestrian': 'Pedonale',
        'mixed_traffic': 'Traffico Misto'
    };
    return uses[use] || use || 'N/A';
}

/**
 * Mostra messaggio di successo
 */
function showSuccessMessage(message) {
    // Implementazione semplice con alert, può essere migliorata con toast/modal
    alert('✅ ' + message);
}

/**
 * Mostra messaggio di errore
 */
function showErrorMessage(title, message) {
    // Implementazione semplice con alert, può essere migliorata con modal
    alert('❌ ' + title + '\n\n' + message);
}

/**
 * Handler globale per il cambio categoria costruttiva
 */
function handleCategoryChange(event) {
    console.log('🎯 ===== handleCategoryChange CHIAMATA =====');

    if (!event || !event.target) {
        console.error('❌ DEBUG: Event o target non valido:', event);
        return;
    }

    const selectedCategory = event.target.value;
    console.log('🎯 DEBUG: Categoria costruttiva selezionata:', selectedCategory);
    console.log('🎯 DEBUG: Event target ID:', event.target.id);
    console.log('🎯 DEBUG: Event type:', event.type);
    console.log('🎯 DEBUG: Timestamp:', new Date().toLocaleTimeString());

    // Log temporaneo per debug - RIMOSSO ALERT FASTIDIOSO
    if (selectedCategory && selectedCategory !== '') {
        console.log('🎉 SUCCESS! Event listener funziona! Categoria:', selectedCategory);
    }

    try {
        console.log('🔄 DEBUG: Aggiornando sottocategorie...');

        updateStructureSubcategories(selectedCategory);
        console.log('✅ DEBUG: updateStructureSubcategories completata');

        updateSlabTypes(selectedCategory);
        console.log('✅ DEBUG: updateSlabTypes completata');

        updateAllFloorsUseOptions(selectedCategory);
        console.log('✅ DEBUG: updateAllFloorsUseOptions completata');

        console.log('🎯 ===== handleCategoryChange COMPLETATA CON SUCCESSO =====');

        // Verifica che le sottocategorie siano effettivamente visibili
        setTimeout(() => {
            const structureDiv = document.getElementById('structure-subcategory');
            const slabDiv = document.getElementById('slab-subcategory');

            if (structureDiv && slabDiv) {
                const structureVisible = structureDiv.style.display !== 'none';
                const slabVisible = slabDiv.style.display !== 'none';

                console.log('🔍 DEBUG: Verifica visibilità sottocategorie:');
                console.log('  - Struttura visibile:', structureVisible);
                console.log('  - Solaio visibile:', slabVisible);

                if (selectedCategory && (!structureVisible || !slabVisible)) {
                    console.error('❌ DEBUG: Sottocategorie non visibili dopo aggiornamento!');
                }
            }
        }, 50);

    } catch (error) {
        console.error('❌ ERRORE CRITICO in handleCategoryChange:', error);
        console.error('❌ ERRORE Stack:', error.stack);
        alert('❌ ERRORE: ' + error.message);
    }
}

/**
 * Funzione di test per verificare il funzionamento dell'event listener
 */
function testCategoryChange() {
    console.log('🔧 TEST: Funzione testCategoryChange chiamata');

    const categorySelect = document.getElementById('im-construction-category');
    if (!categorySelect) {
        console.error('❌ Elemento categoria non trovato!');
        return;
    }

    console.log('🔧 TEST: Elemento categoria trovato, valore corrente:', categorySelect.value);

    // Test 1: Imposta valore "building" e triggera evento
    categorySelect.value = 'building';
    console.log('🔧 TEST: Valore impostato a "building"');

    // Triggera l'evento manualmente
    const event = new Event('change', { bubbles: true });
    categorySelect.dispatchEvent(event);
    console.log('🔧 TEST: Evento change triggerato');

    // Verifica risultato dopo un breve delay
    setTimeout(() => {
        const structureDiv = document.getElementById('structure-subcategory');
        const slabDiv = document.getElementById('slab-subcategory');

        if (structureDiv && slabDiv) {
            const structureVisible = structureDiv.style.display !== 'none';
            const slabVisible = slabDiv.style.display !== 'none';

            console.log('🔧 TEST: Risultati verifica:');
            console.log('  - Struttura visibile:', structureVisible);
            console.log('  - Solaio visibile:', slabVisible);

            if (structureVisible && slabVisible) {
                console.log('✅ TEST SUCCESSO: Event listener funziona correttamente!');
            } else {
                console.log('❌ TEST FALLITO: Event listener non funziona');
                console.error('🔧 TEST: Sottocategorie non visibili dopo evento');
            }
        } else {
            console.log('❌ TEST FALLITO: Elementi sottocategoria non trovati');
            console.error('🔧 TEST: Elementi sottocategoria non trovati nel DOM');
        }
    }, 100);
}

/**
 * Inizializza i gestori per le tipologie costruttive
 */
function initConstructionTypeHandlers() {
    console.log('🔧 DEBUG: initConstructionTypeHandlers - INIZIO');

    const categorySelect = document.getElementById('im-construction-category');
    if (!categorySelect) {
        console.error('❌ DEBUG: Elemento im-construction-category non trovato');
        console.log('🔍 DEBUG: Elementi disponibili:',
            Array.from(document.querySelectorAll('[id*="construction"]')).map(el => el.id));
        return false;
    }

    console.log('✅ DEBUG: Elemento categoria trovato:', categorySelect);
    console.log('🔍 DEBUG: Valore corrente:', categorySelect.value);
    console.log('🔍 DEBUG: Elemento visibile:', categorySelect.offsetParent !== null);

    // Rimuovi TUTTI i possibili listener precedenti
    categorySelect.removeEventListener('change', handleCategoryChange);
    categorySelect.onchange = null;

    // Rimuovi attributi di event handler inline se presenti
    categorySelect.removeAttribute('onchange');

    // Aggiungi il nuovo listener con opzioni specifiche
    categorySelect.addEventListener('change', handleCategoryChange, {
        passive: false,
        capture: false
    });

    // Aggiungi anche un listener diretto come fallback
    categorySelect.onchange = function(event) {
        console.log('🎯 DEBUG: Event listener diretto attivato!');
        console.log('🎯 DEBUG: Valore selezionato:', this.value);
        handleCategoryChange(event);
    };

    console.log('✅ DEBUG: Event listeners aggiunti (addEventListener + onchange)');

    // Test immediato
    console.log('🧪 DEBUG: Eseguendo test immediato...');
    const testEvent = new Event('change', { bubbles: true, cancelable: true });
    categorySelect.value = 'building';
    categorySelect.dispatchEvent(testEvent);

    // Reset dopo test
    setTimeout(() => {
        categorySelect.value = '';
        categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
    }, 100);

    console.log('✅ DEBUG: initConstructionTypeHandlers - COMPLETATO');
    return true;
}

/**
 * Test per verificare che gli event listener siano attivi
 */
function testConstructionTypeHandlers() {
    console.log('DEBUG: testConstructionTypeHandlers - INIZIO');

    const categorySelect = document.getElementById('im-construction-category');
    if (categorySelect) {
        console.log('DEBUG: Elemento categoria trovato, valore corrente:', categorySelect.value);

        // Verifica che l'elemento sia visibile
        const isVisible = categorySelect.offsetParent !== null;
        console.log('DEBUG: Elemento categoria visibile:', isVisible);

        // Verifica che abbia event listeners
        const hasListeners = categorySelect.onchange !== null ||
                            (categorySelect._listeners && categorySelect._listeners.change);
        console.log('DEBUG: Event listeners presenti:', hasListeners);

        // Test programmatico del cambio valore
        console.log('DEBUG: Testando cambio programmatico a "building"...');
        categorySelect.value = 'building';
        categorySelect.dispatchEvent(new Event('change', { bubbles: true }));

        // Verifica che i campi sottocategoria siano apparsi
        setTimeout(() => {
            const structureDiv = document.getElementById('structure-subcategory');
            const slabDiv = document.getElementById('slab-subcategory');

            if (structureDiv && slabDiv) {
                const structureVisible = structureDiv.style.display !== 'none';
                const slabVisible = slabDiv.style.display !== 'none';

                console.log('DEBUG: Sottocategoria struttura visibile:', structureVisible);
                console.log('DEBUG: Sottocategoria solaio visibile:', slabVisible);

                if (structureVisible && slabVisible) {
                    console.log('✅ DEBUG: Test event listener SUCCESSO');
                } else {
                    console.error('❌ DEBUG: Test event listener FALLITO - sottocategorie non visibili');
                }
            } else {
                console.error('❌ DEBUG: Test event listener FALLITO - elementi sottocategoria non trovati');
            }

            // Reset dopo il test
            categorySelect.value = '';
            categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
        }, 50);

    } else {
        console.error('❌ DEBUG: Test fallito - elemento categoria non trovato');
    }

    console.log('DEBUG: testConstructionTypeHandlers - FINE');
}

/**
 * Aggiorna le sottocategorie strutturali in base alla categoria costruttiva
 * @param {string} category - Categoria costruttiva selezionata
 */
function updateStructureSubcategories(category) {
    console.log('DEBUG: updateStructureSubcategories - categoria:', category);

    const subcategoryDiv = document.getElementById('structure-subcategory');
    const structureSelect = document.getElementById('im-structure-type');

    if (!subcategoryDiv || !structureSelect) {
        console.error('DEBUG: Elementi sottocategoria non trovati');
        return;
    }

    let options = '';

    switch(category) {
        case 'bridge':
            options = '<option value="prestressed_concrete">Cemento Armato Precompresso</option>';
            console.log('DEBUG: Opzioni ponte caricate');
            break;
        case 'building':
            options = `
                <option value="concrete">Cemento Armato</option>
                <option value="steel">Acciaio</option>
                <option value="masonry">Muratura</option>
                <option value="wood">Legno</option>
                <option value="mixed">Mista</option>
            `;
            console.log('DEBUG: Opzioni edificio caricate');
            break;
        case 'prefab_building':
            options = '<option value="prestressed_concrete">Cemento Armato Precompresso</option>';
            console.log('DEBUG: Opzioni prefabbricato caricate');
            break;
        default:
            console.log('DEBUG: Nessuna categoria selezionata');
    }

    structureSelect.innerHTML = '<option value="">Seleziona...</option>' + options;
    subcategoryDiv.style.display = category ? 'block' : 'none';

    // Gestione attributo required dinamico
    if (category) {
        structureSelect.setAttribute('required', 'required');
    } else {
        structureSelect.removeAttribute('required');
    }

    // Reset della selezione quando cambia categoria
    structureSelect.value = '';

    console.log('DEBUG: Sottocategorie strutturali aggiornate');
}

/**
 * Aggiorna le tipologie di solaio/impalcato in base alla categoria costruttiva
 * @param {string} category - Categoria costruttiva selezionata
 */
function updateSlabTypes(category) {
    console.log('DEBUG: updateSlabTypes - categoria:', category);

    const slabDiv = document.getElementById('slab-subcategory');
    const slabSelect = document.getElementById('im-slab-type');

    if (!slabDiv || !slabSelect) {
        console.error('DEBUG: Elementi solaio non trovati');
        return;
    }

    let options = '';
    let labelText = 'Tipologia Solaio/Impalcato *';

    switch(category) {
        case 'bridge':
            options = `
                <option value="prestressed_deck">Impalcato Precompresso</option>
                <option value="composite_deck">Impalcato Misto</option>
                <option value="concrete_deck">Impalcato in C.A.</option>
            `;
            labelText = 'Tipologia Impalcato *';
            console.log('DEBUG: Opzioni impalcato ponte caricate');
            break;
        case 'building':
            options = `
                <option value="hollow_brick">Laterocemento</option>
                <option value="solid_slab">Soletta Piena</option>
                <option value="steel_deck">Lamiera Grecata</option>
                <option value="wood_slab">Legno</option>
                <option value="prefab">Prefabbricato</option>
            `;
            labelText = 'Tipologia Solaio *';
            console.log('DEBUG: Opzioni solaio edificio caricate');
            break;
        case 'prefab_building':
            options = `
                <option value="prestressed_slab">Solaio Precompresso</option>
                <option value="prefab_panels">Pannelli Prefabbricati</option>
                <option value="hollow_core">Lastre Alveolari</option>
            `;
            labelText = 'Tipologia Solaio *';
            console.log('DEBUG: Opzioni solaio prefabbricato caricate');
            break;
        default:
            console.log('DEBUG: Nessuna categoria selezionata per solai');
    }

    slabSelect.innerHTML = '<option value="">Seleziona...</option>' + options;
    slabDiv.style.display = category ? 'block' : 'none';

    // Gestione attributo required dinamico
    if (category) {
        slabSelect.setAttribute('required', 'required');
    } else {
        slabSelect.removeAttribute('required');
    }

    // Aggiorna il label
    const label = slabDiv.querySelector('label');
    if (label) {
        label.textContent = labelText;
    }

    // Reset della selezione quando cambia categoria
    slabSelect.value = '';

    console.log('DEBUG: Tipologie solaio aggiornate');
}

/**
 * Reset dello stato iniziale dei campi tipologie costruttive
 */
function resetConstructionTypeFields() {
    console.log('DEBUG: resetConstructionTypeFields - INIZIO');

    // Reset categoria principale
    const categorySelect = document.getElementById('im-construction-category');
    if (categorySelect) {
        categorySelect.value = '';
    }

    // Nascondi e reset sottocategorie
    const structureDiv = document.getElementById('structure-subcategory');
    const slabDiv = document.getElementById('slab-subcategory');
    const structureSelect = document.getElementById('im-structure-type');
    const slabSelect = document.getElementById('im-slab-type');

    if (structureDiv && structureSelect) {
        structureDiv.style.display = 'none';
        structureSelect.innerHTML = '<option value="">Seleziona...</option>';
        structureSelect.removeAttribute('required');
        structureSelect.value = '';
    }

    if (slabDiv && slabSelect) {
        slabDiv.style.display = 'none';
        slabSelect.innerHTML = '<option value="">Seleziona...</option>';
        slabSelect.removeAttribute('required');
        slabSelect.value = '';
    }

    console.log('DEBUG: resetConstructionTypeFields - FINE');
}

/**
 * Ottiene le opzioni d'uso per la categoria costruttiva correntemente selezionata
 * @returns {string} HTML delle opzioni per destinazione d'uso
 */
function getUseOptionsForCurrentCategory() {
    const categorySelect = document.getElementById('im-construction-category');
    const currentCategory = categorySelect ? categorySelect.value : 'building';

    console.log('DEBUG: getUseOptionsForCurrentCategory - categoria corrente:', currentCategory);

    if (currentCategory === 'bridge') {
        return `
            <option value="highway">Autostrada</option>
            <option value="urban_road">Strada Urbana</option>
            <option value="railway">Ferrovia</option>
            <option value="pedestrian">Pedonale</option>
            <option value="mixed_traffic">Traffico Misto</option>
        `;
    }

    // Per edifici e prefabbricati, destinazioni standard
    return `
        <option value="residential">Residenziale</option>
        <option value="office">Uffici</option>
        <option value="commercial">Commerciale</option>
        <option value="industrial">Industriale</option>
        <option value="storage">Magazzino</option>
    `;
}

/**
 * Aggiorna le destinazioni d'uso per tutti i piani esistenti
 * @param {string} category - Categoria costruttiva selezionata
 */
function updateAllFloorsUseOptions(category) {
    console.log('DEBUG: updateAllFloorsUseOptions - categoria:', category);

    const floorUseSelects = document.querySelectorAll('[id$="-use"]');
    const useOptions = getUseOptionsForCategory(category);

    floorUseSelects.forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">Seleziona...</option>' + useOptions;

        // Prova a mantenere la selezione precedente se compatibile
        if (currentValue && select.querySelector(`option[value="${currentValue}"]`)) {
            select.value = currentValue;
        }
    });

    console.log('DEBUG: Destinazioni d\'uso aggiornate per tutti i piani');
}

/**
 * Aggiorna le destinazioni d'uso in base alla categoria costruttiva
 * @param {string} category - Categoria costruttiva selezionata
 * @returns {string} HTML delle opzioni per destinazione d'uso
 */
function getUseOptionsForCategory(category) {
    console.log('DEBUG: getUseOptionsForCategory - categoria:', category);

    if (category === 'bridge') {
        return `
            <option value="highway">Autostrada</option>
            <option value="urban_road">Strada Urbana</option>
            <option value="railway">Ferrovia</option>
            <option value="pedestrian">Pedonale</option>
            <option value="mixed_traffic">Traffico Misto</option>
        `;
    }

    // Per edifici e prefabbricati, destinazioni standard
    return `
        <option value="residential">Residenziale</option>
        <option value="office">Uffici</option>
        <option value="commercial">Commerciale</option>
        <option value="industrial">Industriale</option>
        <option value="storage">Magazzino</option>
    `;
}

// Esporta le funzioni per uso globale
window.initInertialMassModal = initInertialMassModal;
window.closeInertialMassModal = closeInertialMassModal;
window.addFloor = addFloor;
window.removeFloor = removeFloor;
window.resetCalculation = resetCalculation;
window.saveResults = saveResults;
window.updateStructureSubcategories = updateStructureSubcategories;
window.updateSlabTypes = updateSlabTypes;
window.handleCategoryChange = handleCategoryChange;
window.testCategoryChange = testCategoryChange;
window.displayResults = displayResults; // Aggiunta per test
window.initConstructionTypeHandlers = initConstructionTypeHandlers; // Aggiunta per debug
window.testConstructionTypeHandlers = testConstructionTypeHandlers; // Aggiunta per debug

// Funzione di debug globale per event listener
window.debugEventListeners = function() {
    console.log('🔍 ===== DEBUG EVENT LISTENERS =====');

    const categorySelect = document.getElementById('im-construction-category');
    if (!categorySelect) {
        console.error('❌ Elemento categoria non trovato');
        return;
    }

    console.log('✅ Elemento categoria trovato:', categorySelect);
    console.log('📊 Proprietà elemento:');
    console.log('  - ID:', categorySelect.id);
    console.log('  - Valore:', categorySelect.value);
    console.log('  - Visibile:', categorySelect.offsetParent !== null);
    console.log('  - onchange:', categorySelect.onchange !== null);
    console.log('  - Attributo onchange:', categorySelect.getAttribute('onchange'));

    // Test programmatico
    console.log('🧪 Eseguendo test programmatico...');
    const originalValue = categorySelect.value;

    categorySelect.value = 'building';
    const testEvent = new Event('change', { bubbles: true, cancelable: true });
    categorySelect.dispatchEvent(testEvent);

    setTimeout(() => {
        const structureDiv = document.getElementById('structure-subcategory');
        const slabDiv = document.getElementById('slab-subcategory');

        if (structureDiv && slabDiv) {
            const structureVisible = structureDiv.style.display !== 'none';
            const slabVisible = slabDiv.style.display !== 'none';

            console.log('📋 Risultati test:');
            console.log('  - Struttura visibile:', structureVisible);
            console.log('  - Solaio visibile:', slabVisible);

            if (structureVisible && slabVisible) {
                console.log('✅ TEST SUCCESSO: Event listener funziona!');
            } else {
                console.log('❌ TEST FALLITO: Event listener non funziona');
            }
        }

        // Ripristina valore originale
        categorySelect.value = originalValue;
        categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
    }, 100);

    console.log('🔍 ===== FINE DEBUG =====');
};

// =====================================================
// GESTIONE TAB SYSTEM
// =====================================================

/**
 * Inizializza il sistema di tab nel modal
 */
function initTabSystem() {
    console.log('DEBUG: Inizializzando sistema tab');

    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    console.log('DEBUG: Tab buttons trovati:', tabButtons.length);
    console.log('DEBUG: Tab contents trovati:', tabContents.length);

    // Aggiungi event listener ai pulsanti tab
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            console.log('DEBUG: Cliccato tab:', targetTab);

            // Rimuovi classe active da tutti i tab
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Aggiungi classe active al tab selezionato
            this.classList.add('active');
            const targetContent = document.getElementById(targetTab + '-tab');
            if (targetContent) {
                targetContent.classList.add('active');
                console.log('DEBUG: Tab attivato:', targetTab);

                // Se è il tab spettri, inizializza il visualizzatore
                if (targetTab === 'spectrum') {
                    initSpectrumTab();
                }
            }
        });
    });
}

/**
 * Inizializza il tab spettri di risposta
 */
function initSpectrumTab() {
    console.log('DEBUG: Inizializzando tab spettri');

    // Carica le dipendenze necessarie
    loadSpectrumDependencies().then(() => {
        console.log('DEBUG: Dipendenze caricate, inizializzando visualizzatore');
        initSpectrumVisualizerAfterLoad();
    }).catch(error => {
        console.error('Errore caricamento dipendenze:', error);
        document.getElementById('spectrum-visualizer-container').innerHTML = `
            <div style="text-align: center; padding: 2rem; color: #dc3545;">
                <h4>❌ Errore Caricamento</h4>
                <p>Impossibile caricare le librerie necessarie: ${error.message}</p>
                <button onclick="initSpectrumTab()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #D97706; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Riprova
                </button>
            </div>
        `;
    });
}

/**
 * Carica le dipendenze necessarie per gli spettri
 */
function loadSpectrumDependencies() {
    return new Promise((resolve, reject) => {
        console.log('DEBUG: Caricando dipendenze...');

        // Mostra loading
        document.getElementById('spectrum-visualizer-container').innerHTML = `
            <div style="text-align: center; padding: 2rem;">
                <h4>🔄 Caricamento...</h4>
                <p>Caricamento librerie per spettri di risposta...</p>
            </div>
        `;

        const dependencies = [];

        // Verifica e carica Chart.js (versione UMD per compatibilità)
        if (typeof Chart === 'undefined') {
            console.log('DEBUG: Caricando Chart.js UMD...');
            dependencies.push(loadScript('https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js'));
        }

        // Verifica e carica SpectrumVisualizerModal
        if (typeof SpectrumVisualizerModal === 'undefined') {
            console.log('DEBUG: Caricando SpectrumVisualizerModal...');
            // Prova prima percorso relativo, poi assoluto se fallisce
            const spectrumPath = window.location.pathname.includes('/inertial_mass/')
                ? '../../js/SpectrumVisualizerModal.js'
                : '/progetti/asdp/js/SpectrumVisualizerModal.js';
            console.log('DEBUG: Percorso calcolato:', spectrumPath);
            dependencies.push(loadScript(spectrumPath));
        }

        if (dependencies.length === 0) {
            console.log('DEBUG: Tutte le dipendenze già caricate');
            resolve();
            return;
        }

        Promise.all(dependencies)
            .then(() => {
                console.log('DEBUG: Tutte le dipendenze caricate con successo');
                // Aspetta un momento per assicurarsi che tutto sia inizializzato
                setTimeout(() => {
                    console.log('DEBUG: Verifica finale disponibilità Chart:', typeof Chart);
                    console.log('DEBUG: Verifica finale disponibilità SpectrumVisualizerModal:', typeof SpectrumVisualizerModal);
                    resolve();
                }, 200);
            })
            .catch(reject);
    });
}

/**
 * Carica uno script dinamicamente
 */
function loadScript(src) {
    return new Promise((resolve, reject) => {
        console.log(`DEBUG: Tentativo caricamento script: ${src}`);

        const script = document.createElement('script');
        script.src = src;

        // Debug URL finale
        console.log(`DEBUG: URL finale script: ${script.src}`);

        script.onload = () => {
            console.log(`DEBUG: Script caricato con successo: ${src}`);
            resolve();
        };
        script.onerror = () => {
            console.error(`DEBUG: Errore caricamento script: ${src}`);
            console.error(`DEBUG: URL tentato: ${script.src}`);
            reject(new Error(`Impossibile caricare ${src}`));
        };
        document.head.appendChild(script);
    });
}

/**
 * Inizializza il visualizzatore dopo il caricamento delle dipendenze
 */
function initSpectrumVisualizerAfterLoad() {
    console.log('DEBUG: Inizializzando visualizzatore dopo caricamento dipendenze');

    // Verifica finale che tutto sia disponibile
    if (typeof Chart === 'undefined') {
        throw new Error('Chart.js non disponibile dopo il caricamento');
    }

    if (typeof SpectrumVisualizerModal === 'undefined') {
        throw new Error('SpectrumVisualizerModal non disponibile dopo il caricamento');
    }

    // Verifica che ci siano dati sismici
    if (!inertialMassState.seismicData) {
        console.error('Dati sismici non disponibili');
        document.getElementById('spectrum-visualizer-container').innerHTML = `
            <div style="text-align: center; padding: 2rem; color: #ffc107;">
                <h4>⚠️ Attenzione</h4>
                <p>Dati sismici non disponibili. Esegui prima un calcolo sismico.</p>
            </div>
        `;
        return;
    }

    try {
        // Inizializza il visualizzatore se non esiste già
        if (!window.spectrumVisualizerModal) {
            console.log('DEBUG: Creando nuovo SpectrumVisualizerModal');
            window.spectrumVisualizerModal = new SpectrumVisualizerModal('spectrum-visualizer-container');
        }

        // Prepara i parametri per il calcolo spettro
        const spectrumParams = {
            ag: inertialMassState.seismicData.ag,
            F0: inertialMassState.seismicData.F0,
            TC: inertialMassState.seismicData.TC,
            soil_category: inertialMassState.seismicData.soil_category || 'C',
            topographic_category: 'T1', // Default
            damping_ratio: 5.0 // Default
        };

        console.log('DEBUG: Parametri spettro:', spectrumParams);

        // Calcola e visualizza confronto spettri ANTE/POST se disponibili i risultati
        if (inertialMassState.lastCalculationResults) {
            calculateAndDisplaySpectrumComparison(spectrumParams);
        } else {
            // Altrimenti mostra spettro elastico di default
            calculateAndDisplaySpectrum(spectrumParams);
        }

    } catch (error) {
        console.error('Errore inizializzazione tab spettri:', error);
        document.getElementById('spectrum-visualizer-container').innerHTML = `
            <div style="text-align: center; padding: 2rem; color: #dc3545;">
                <h4>❌ Errore</h4>
                <p>Errore durante l'inizializzazione: ${error.message}</p>
            </div>
        `;
    }
}

/**
 * Calcola e visualizza lo spettro
 */
async function calculateAndDisplaySpectrum(params) {
    try {
        console.log('DEBUG: Calcolando spettro con parametri:', params);

        // Mostra loading
        document.getElementById('spectrum-visualizer-container').innerHTML = `
            <div style="text-align: center; padding: 2rem;">
                <h4>🔄 Calcolo in corso...</h4>
                <p>Generazione spettro di risposta...</p>
            </div>
        `;

        // Chiama API per calcolo spettro elastico
        const queryString = new URLSearchParams(params).toString();
        const response = await fetch(`api/spectrum_service.php?action=elastic_spectrum&${queryString}`);

        if (!response.ok) {
            throw new Error(`Errore API: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error || 'Errore sconosciuto');
        }

        console.log('DEBUG: Spettro calcolato:', data.data);

        // Reinizializza il visualizzatore con i nuovi dati
        window.spectrumVisualizerModal = new SpectrumVisualizerModal('spectrum-visualizer-container');
        window.spectrumVisualizerModal.renderElasticSpectrum(data.data);

        console.log('DEBUG: Spettro visualizzato con successo');

    } catch (error) {
        console.error('Errore calcolo spettro:', error);
        document.getElementById('spectrum-visualizer-container').innerHTML = `
            <div style="text-align: center; padding: 2rem; color: #dc3545;">
                <h4>❌ Errore Calcolo</h4>
                <p>${error.message}</p>
                <button onclick="initSpectrumTab()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #D97706; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Riprova
                </button>
            </div>
        `;
    }
}

/**
 * Calcola e visualizza il confronto sovrapposto degli spettri ANTE/POST
 */
async function calculateAndDisplaySpectrumComparison(baseParams) {
    try {
        console.log('DEBUG: Calcolando confronto spettri ANTE/POST con parametri:', baseParams);

        // Mostra loading
        document.getElementById('spectrum-visualizer-container').innerHTML = `
            <div style="text-align: center; padding: 2rem;">
                <h4>🔄 Calcolo in corso...</h4>
                <p>Generazione confronto spettri ANTE/POST...</p>
            </div>
        `;

        // Ottieni i dati di input e risultati dall'ultimo calcolo
        const inputData = inertialMassState.lastInputData || {};
        const results = inertialMassState.lastCalculationResults || {};

        // Calcola smorzamento ANTE e POST
        const dampingBefore = 5.0; // Smorzamento strutturale standard
        const dampingAfter = calculateEquivalentDamping(inputData, results);

        // Prepara parametri per entrambi gli spettri
        const paramsBefore = { ...baseParams, damping_ratio: dampingBefore };
        const paramsAfter = { ...baseParams, damping_ratio: dampingAfter };

        // Calcola entrambi gli spettri in parallelo
        const [responseBefore, responseAfter] = await Promise.all([
            fetch(`api/spectrum_service.php?action=elastic_spectrum&${new URLSearchParams(paramsBefore).toString()}`),
            fetch(`api/spectrum_service.php?action=elastic_spectrum&${new URLSearchParams(paramsAfter).toString()}`)
        ]);

        if (!responseBefore.ok || !responseAfter.ok) {
            throw new Error('Errore nel calcolo degli spettri');
        }

        const dataBefore = await responseBefore.json();
        const dataAfter = await responseAfter.json();

        if (!dataBefore.success || !dataAfter.success) {
            throw new Error('Errore nei dati degli spettri');
        }

        // Crea oggetto dati di confronto
        const comparisonData = {
            before_spectrum: dataBefore.data,
            after_spectrum: dataAfter.data,
            comparison_metadata: {
                damping_before: dampingBefore,
                damping_after: dampingAfter
            },
            reduction_analysis: calculateSpectrumReduction(
                dataBefore.data.spectrum_points,
                dataAfter.data.spectrum_points
            )
        };

        console.log('DEBUG: Dati confronto spettri:', comparisonData);

        // Reinizializza il visualizzatore con i dati di confronto
        window.spectrumVisualizerModal = new SpectrumVisualizerModal('spectrum-visualizer-container');
        window.spectrumVisualizerModal.renderComparison(comparisonData);

        console.log('DEBUG: Confronto spettri visualizzato con successo');

    } catch (error) {
        console.error('Errore calcolo confronto spettri:', error);
        document.getElementById('spectrum-visualizer-container').innerHTML = `
            <div style="text-align: center; padding: 2rem; color: #dc3545;">
                <h4>❌ Errore Calcolo Confronto</h4>
                <p>${error.message}</p>
                <button onclick="initSpectrumTab()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #D97706; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Riprova
                </button>
            </div>
        `;
    }
}

// Esporta le funzioni per l'uso globale
window.initInertialMassModal = initInertialMassModal;
window.closeInertialMassModal = closeInertialMassModal;
window.addFloor = addFloor;
window.removeFloor = removeFloor;
window.initTabSystem = initTabSystem;
window.initSpectrumTab = initSpectrumTab;
window.loadSpectrumDependencies = loadSpectrumDependencies;
window.loadScript = loadScript;
window.initSpectrumVisualizerAfterLoad = initSpectrumVisualizerAfterLoad;
window.calculateAndDisplaySpectrum = calculateAndDisplaySpectrum;
window.calculateAndDisplaySpectrumComparison = calculateAndDisplaySpectrumComparison;
