
================================================================================
[2025-06-06 11:15:44] RICHIESTA AI - Session: 8uh902mnct6t7a3t7kkv05rmjp - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.76151,
        "lon": 12.655854
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.169,
        "F0": 2.597,
        "TC": 0.273,
        "soil_category": "C"
    },
    "building": {
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 2008,
        "floors": [
            {
                "level": 1,
                "area": 100,
                "height": 4,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "deepseek",
    "fallback_provider": "gemini",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.76151, 12.655854
- Zona sismica: 3
- ag: 0.169 g
- F0: 2.597
- TC*: 0.273 s
- Categoria sottosuolo: C

DATI STRUTTURA:
- Tipologia: concrete
- Solaio: hollow_brick
- Anno costruzione: 2008

PIANI:
- Piano 1: Area 100 m², Altezza 4 m, Uso: residential

Calcola:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Forza sismica totale alla base (kN)
5. Distribuzione delle forze per piano (kN)
6. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-14 00:02:02] RICHIESTA AI - Session: denctnak4df5gpen01e0cqmtds - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808087,
        "lon": 12.679358
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 400,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808087, 12.679358
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Tipologia: concrete
- Solaio: hollow_brick
- Anno costruzione: 2000

PIANI:
- Piano 1: Area 400 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-14 00:05:00] RICHIESTA AI - Session: am55li6gfmg1bfs5e07p6vu38o - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808087,
        "lon": 12.679358
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 300,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808087, 12.679358
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Tipologia: concrete
- Solaio: hollow_brick
- Anno costruzione: 2000

PIANI:
- Piano 1: Area 300 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-14 00:13:29] RICHIESTA AI - Session: aah7g6bgoko2juoh9mplpukjgg - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808087,
        "lon": 12.679358
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 150,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808087, 12.679358
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Tipologia: concrete
- Solaio: hollow_brick
- Anno costruzione: 2000

PIANI:
- Piano 1: Area 150 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-14 00:18:00] RICHIESTA AI - Session: qkplehaqm8hhg0i0ucufodc0l7 - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808087,
        "lon": 12.679358
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 120,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808087, 12.679358
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Tipologia: concrete
- Solaio: hollow_brick
- Anno costruzione: 2000

PIANI:
- Piano 1: Area 120 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-14 22:16:13] RICHIESTA AI - Session: r2eksfr4vuao5nqs4q5ikcqpss - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808022,
        "lon": 12.679356
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 200,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808022, 12.679356
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Tipologia: concrete
- Solaio: hollow_brick
- Anno costruzione: 2000

PIANI:
- Piano 1: Area 200 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 07:57:17] RICHIESTA AI - Session: nti7b1ksubvc08rk3nnldmelmr - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807962,
        "lon": 12.679433
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "bridge",
        "structure_type": "prestressed_concrete",
        "slab_type": "prestressed_deck",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 200,
                "height": 1,
                "use": "highway"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807962, 12.679433
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Bridge
- Tipologia strutturale: prestressed_concrete
- Tipologia impalcato: prestressed_deck
- Anno costruzione: 2000

CONSIDERAZIONI SPECIFICHE PER PONTI:
- Utilizzare parametri per cemento armato precompresso
- Considerare carichi da traffico secondo NTC 2018
- Valutare effetti dinamici specifici per ponti
- Formula periodo: T = 0.02 × L^1.2 per ponti precompressi

CAMPATE/SEZIONI:
- Sezione 1: Area 200 m², Luce 1 m, Traffico: highway

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni sezione (kN/m²)
2. Massa totale del ponte (tonnellate)
3. Periodo fondamentale T1 (secondi) - usa formula specifica per ponti
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale (kN) considerando carichi da traffico
6. Distribuzione delle forze per sezione (kN)
7. Breve analisi della vulnerabilità sismica per ponti

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 08:16:03] RICHIESTA AI - Session: i0bqjf82r91qq6hpseu6jj9li3 - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807967,
        "lon": 12.679396
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.196,
        "F0": 2.555,
        "TC": 0.122,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "bridge",
        "structure_type": "prestressed_concrete",
        "slab_type": "prestressed_deck",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 200,
                "height": 1,
                "use": "highway"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807967, 12.679396
- Zona sismica: 3
- ag: 0.196 g
- F0: 2.555
- TC*: 0.122 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Bridge
- Tipologia strutturale: prestressed_concrete
- Tipologia impalcato: prestressed_deck
- Anno costruzione: 2000

CONSIDERAZIONI SPECIFICHE PER PONTI:
- Utilizzare parametri per cemento armato precompresso
- Considerare carichi da traffico secondo NTC 2018
- Valutare effetti dinamici specifici per ponti
- Formula periodo: T = 0.02 × L^1.2 per ponti precompressi

CAMPATE/SEZIONI:
- Sezione 1: Area 200 m², Luce 1 m, Traffico: highway

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni sezione (kN/m²)
2. Massa totale del ponte (tonnellate)
3. Periodo fondamentale T1 (secondi) - usa formula specifica per ponti
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale (kN) considerando carichi da traffico
6. Distribuzione delle forze per sezione (kN)
7. Breve analisi della vulnerabilità sismica per ponti

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 09:03:10] RICHIESTA AI - Session: fulhm3latqm02g2grpu1f085mb - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807871,
        "lon": 12.67959
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "bridge",
        "structure_type": "prestressed_concrete",
        "slab_type": "prestressed_deck",
        "construction_year": 2020,
        "floors": [
            {
                "level": 1,
                "area": 500,
                "height": 1,
                "use": "highway"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807871, 12.67959
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Bridge
- Tipologia strutturale: prestressed_concrete
- Tipologia impalcato: prestressed_deck
- Anno costruzione: 2020

CONSIDERAZIONI SPECIFICHE PER PONTI:
- Utilizzare parametri per cemento armato precompresso
- Considerare carichi da traffico secondo NTC 2018
- Valutare effetti dinamici specifici per ponti
- Formula periodo: T = 0.02 × L^1.2 per ponti precompressi

CAMPATE/SEZIONI:
- Sezione 1: Area 500 m², Luce 1 m, Traffico: highway

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni sezione (kN/m²)
2. Massa totale del ponte (tonnellate)
3. Periodo fondamentale T1 (secondi) - usa formula specifica per ponti
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale (kN) considerando carichi da traffico
6. Distribuzione delle forze per sezione (kN)
7. Breve analisi della vulnerabilità sismica per ponti

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 09:41:23] RICHIESTA AI - Session: qndb02kggi65l86qdhsgej6dvk - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807903,
        "lon": 12.679472
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "bridge",
        "structure_type": "prestressed_concrete",
        "slab_type": "prestressed_deck",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 500,
                "height": 1,
                "use": "urban_road"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807903, 12.679472
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Bridge
- Tipologia strutturale: prestressed_concrete
- Tipologia impalcato: prestressed_deck
- Anno costruzione: 2000

CONSIDERAZIONI SPECIFICHE PER PONTI:
- Utilizzare parametri per cemento armato precompresso
- Considerare carichi da traffico secondo NTC 2018
- Valutare effetti dinamici specifici per ponti
- Formula periodo: T = 0.02 × L^1.2 per ponti precompressi

CAMPATE/SEZIONI:
- Sezione 1: Area 500 m², Luce 1 m, Traffico: urban_road

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni sezione (kN/m²)
2. Massa totale del ponte (tonnellate)
3. Periodo fondamentale T1 (secondi) - usa formula specifica per ponti
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale (kN) considerando carichi da traffico
6. Distribuzione delle forze per sezione (kN)
7. Breve analisi della vulnerabilità sismica per ponti

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 09:42:44] RICHIESTA AI - Session: qndb02kggi65l86qdhsgej6dvk - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807903,
        "lon": 12.679472
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "steel",
        "slab_type": "solid_slab",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 300,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807903, 12.679472
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: steel
- Tipologia solaio: solid_slab
- Anno costruzione: 2000

PIANI:
- Piano 1: Area 300 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 10:29:54] RICHIESTA AI - Session: sh8eoqkhk9efnereidm6v4s096 - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807895,
        "lon": 12.679568
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 120,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807895, 12.679568
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: hollow_brick
- Anno costruzione: 2000

PIANI:
- Piano 1: Area 120 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 11:16:01] RICHIESTA AI - Session: joh4fkc4muj6otg1srjmtulm4k - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807927,
        "lon": 12.679482
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "masonry",
        "slab_type": "steel_deck",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 200,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807927, 12.679482
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: masonry
- Tipologia solaio: steel_deck
- Anno costruzione: 2000

PIANI:
- Piano 1: Area 200 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 11:26:23] RICHIESTA AI - Session: n27vmol5u62qphbgflglb4m1cm - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808087,
        "lon": 12.679358
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "prefab_building",
        "structure_type": "prestressed_concrete",
        "slab_type": "prestressed_slab",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 300,
                "height": 3,
                "use": "commercial"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808087, 12.679358
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Prefab_building
- Tipologia strutturale: prestressed_concrete
- Tipologia solaio: prestressed_slab
- Anno costruzione: 2000

CONSIDERAZIONI SPECIFICHE PER PREFABBRICATI:
- Utilizzare parametri per cemento armato precompresso
- Considerare collegamenti tra elementi prefabbricati
- Valutare rigidezza del sistema strutturale
- Formula periodo: T = 0.070 × H^0.75 per precompresso

PIANI:
- Piano 1: Area 300 m², Altezza 3 m, Uso: commercial

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 11:32:12] RICHIESTA AI - Session: n27vmol5u62qphbgflglb4m1cm - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808087,
        "lon": 12.679358
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "bridge",
        "structure_type": "prestressed_concrete",
        "slab_type": "prestressed_deck",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 300,
                "height": 1,
                "use": "railway"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808087, 12.679358
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Bridge
- Tipologia strutturale: prestressed_concrete
- Tipologia impalcato: prestressed_deck
- Anno costruzione: 2000

CONSIDERAZIONI SPECIFICHE PER PONTI:
- Utilizzare parametri per cemento armato precompresso
- Considerare carichi da traffico secondo NTC 2018
- Valutare effetti dinamici specifici per ponti
- Formula periodo: T = 0.02 × L^1.2 per ponti precompressi

CAMPATE/SEZIONI:
- Sezione 1: Area 300 m², Luce 1 m, Traffico: railway

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni sezione (kN/m²)
2. Massa totale del ponte (tonnellate)
3. Periodo fondamentale T1 (secondi) - usa formula specifica per ponti
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale (kN) considerando carichi da traffico
6. Distribuzione delle forze per sezione (kN)
7. Breve analisi della vulnerabilità sismica per ponti

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 18:59:10] RICHIESTA AI - Session: 4sof30iqagqqpult0rgkg68gqa - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807871,
        "lon": 12.679407
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "prefab_building",
        "structure_type": "prestressed_concrete",
        "slab_type": "prestressed_slab",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 500,
                "height": 5,
                "use": "industrial"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807871, 12.679407
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Prefab_building
- Tipologia strutturale: prestressed_concrete
- Tipologia solaio: prestressed_slab
- Anno costruzione: 2000

CONSIDERAZIONI SPECIFICHE PER PREFABBRICATI:
- Utilizzare parametri per cemento armato precompresso
- Considerare collegamenti tra elementi prefabbricati
- Valutare rigidezza del sistema strutturale
- Formula periodo: T = 0.070 × H^0.75 per precompresso

PIANI:
- Piano 1: Area 500 m², Altezza 5 m, Uso: industrial

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 20:00:41] RICHIESTA AI - Session: vn186i0314jfljo7hurm11u9os - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807933,
        "lon": 12.679466
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 1970,
        "floors": [
            {
                "level": 1,
                "area": 200,
                "height": 3,
                "use": "commercial"
            },
            {
                "level": 2,
                "area": 200,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807933, 12.679466
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: hollow_brick
- Anno costruzione: 1970

PIANI:
- Piano 1: Area 200 m², Altezza 3 m, Uso: commercial
- Piano 2: Area 200 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 20:18:16] RICHIESTA AI - Session: no8ojok1sqcdmqmfueelmh0e7c - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807869,
        "lon": 12.67945
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "solid_slab",
        "construction_year": 1930,
        "floors": [
            {
                "level": 1,
                "area": 400,
                "height": 5,
                "use": "commercial"
            },
            {
                "level": 2,
                "area": 300,
                "height": 3,
                "use": "office"
            },
            {
                "level": 3,
                "area": 300,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807869, 12.67945
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: solid_slab
- Anno costruzione: 1930

PIANI:
- Piano 1: Area 400 m², Altezza 5 m, Uso: commercial
- Piano 2: Area 300 m², Altezza 3 m, Uso: office
- Piano 3: Area 300 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 20:27:24] RICHIESTA AI - Session: 9uqebsemi68moeq0ek4sman845 - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808087,
        "lon": 12.679358
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "bridge",
        "structure_type": "prestressed_concrete",
        "slab_type": "prestressed_deck",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 2000,
                "height": 1,
                "use": "highway"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808087, 12.679358
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Bridge
- Tipologia strutturale: prestressed_concrete
- Tipologia impalcato: prestressed_deck
- Anno costruzione: 2000

CONSIDERAZIONI SPECIFICHE PER PONTI:
- Utilizzare parametri per cemento armato precompresso
- Considerare carichi da traffico secondo NTC 2018
- Valutare effetti dinamici specifici per ponti
- Formula periodo: T = 0.02 × L^1.2 per ponti precompressi

CAMPATE/SEZIONI:
- Sezione 1: Area 2000 m², Luce 1 m, Traffico: highway

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni sezione (kN/m²)
2. Massa totale del ponte (tonnellate)
3. Periodo fondamentale T1 (secondi) - usa formula specifica per ponti
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale (kN) considerando carichi da traffico
6. Distribuzione delle forze per sezione (kN)
7. Breve analisi della vulnerabilità sismica per ponti

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 23:15:48] RICHIESTA AI - Session: h8ft096hqnp9e33151b4dnrk9b - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807953,
        "lon": 12.679464
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 1969,
        "floors": [
            {
                "level": 1,
                "area": 200,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807953, 12.679464
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: hollow_brick
- Anno costruzione: 1969

PIANI:
- Piano 1: Area 200 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-15 23:44:00] RICHIESTA AI - Session: ltbn0n3t8inu0ffdtvok0itkt0 - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807943,
        "lon": 12.679386
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 1970,
        "floors": [
            {
                "level": 1,
                "area": 300,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807943, 12.679386
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: hollow_brick
- Anno costruzione: 1970

PIANI:
- Piano 1: Area 300 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-16 00:17:18] RICHIESTA AI - Session: ltbn0n3t8inu0ffdtvok0itkt0 - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808087,
        "lon": 12.679358
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 1970,
        "floors": [
            {
                "level": 1,
                "area": 120,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808087, 12.679358
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: hollow_brick
- Anno costruzione: 1970

PIANI:
- Piano 1: Area 120 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-16 00:42:36] RICHIESTA AI - Session: gdqoqqglm2dh8nlsjfpojf3glm - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808087,
        "lon": 12.679358
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 1970,
        "floors": [
            {
                "level": 1,
                "area": 300,
                "height": 3,
                "use": "residential"
            },
            {
                "level": 2,
                "area": 300,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808087, 12.679358
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: hollow_brick
- Anno costruzione: 1970

PIANI:
- Piano 1: Area 300 m², Altezza 3 m, Uso: residential
- Piano 2: Area 300 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-16 10:01:59] RICHIESTA AI - Session: rn31530dgc3tmpqor1afb7hsqs - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808087,
        "lon": 12.679358
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "solid_slab",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 400,
                "height": 3,
                "use": "storage"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808087, 12.679358
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: solid_slab
- Anno costruzione: 2000

PIANI:
- Piano 1: Area 400 m², Altezza 3 m, Uso: storage

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-16 10:34:34] RICHIESTA AI - Session: rn31530dgc3tmpqor1afb7hsqs - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.841807,
        "lon": 12.599834
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.166,
        "F0": 2.585,
        "TC": 0.273,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 300,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.841807, 12.599834
- Zona sismica: 3
- ag: 0.166 g
- F0: 2.585
- TC*: 0.273 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: hollow_brick
- Anno costruzione: 2000

PIANI:
- Piano 1: Area 300 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-16 10:36:28] RICHIESTA AI - Session: ao1fhjfqeh4abn1es99236qktv - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807951,
        "lon": 12.679461
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 1970,
        "floors": [
            {
                "level": 1,
                "area": 300,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807951, 12.679461
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: hollow_brick
- Anno costruzione: 1970

PIANI:
- Piano 1: Area 300 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-16 10:46:24] RICHIESTA AI - Session: velvq91ilo3cmf2r2l8obhtsft - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808016,
        "lon": 12.679419
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 300,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808016, 12.679419
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: hollow_brick
- Anno costruzione: 2000

PIANI:
- Piano 1: Area 300 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-16 10:50:04] RICHIESTA AI - Session: kpc6lera6f5lpdba9ssgoalb33 - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807823,
        "lon": 12.679536
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 1970,
        "floors": [
            {
                "level": 1,
                "area": 200,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807823, 12.679536
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: hollow_brick
- Anno costruzione: 1970

PIANI:
- Piano 1: Area 200 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-16 10:52:54] RICHIESTA AI - Session: ugev8bl2gptglrrme4g3n76lt0 - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 43.654298,
        "lon": 10.62113
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.131,
        "F0": 2.505,
        "TC": 0.272,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "prefab_building",
        "structure_type": "prestressed_concrete",
        "slab_type": "prestressed_slab",
        "construction_year": 1980,
        "floors": [
            {
                "level": 1,
                "area": 1500,
                "height": 5,
                "use": "storage"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 43.654298, 10.62113
- Zona sismica: 3
- ag: 0.131 g
- F0: 2.505
- TC*: 0.272 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Prefab_building
- Tipologia strutturale: prestressed_concrete
- Tipologia solaio: prestressed_slab
- Anno costruzione: 1980

CONSIDERAZIONI SPECIFICHE PER PREFABBRICATI:
- Utilizzare parametri per cemento armato precompresso
- Considerare collegamenti tra elementi prefabbricati
- Valutare rigidezza del sistema strutturale
- Formula periodo: T = 0.070 × H^0.75 per precompresso

PIANI:
- Piano 1: Area 1500 m², Altezza 5 m, Uso: storage

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-16 12:21:48] RICHIESTA AI - Session: ugev8bl2gptglrrme4g3n76lt0 - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.818542,
        "lon": 12.688548
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.203,
        "F0": 2.542,
        "TC": 0.122,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "prefab_building",
        "structure_type": "prestressed_concrete",
        "slab_type": "prestressed_slab",
        "construction_year": 1980,
        "floors": [
            {
                "level": 1,
                "area": 1000,
                "height": 5,
                "use": "commercial"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.818542, 12.688548
- Zona sismica: 3
- ag: 0.203 g
- F0: 2.542
- TC*: 0.122 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Prefab_building
- Tipologia strutturale: prestressed_concrete
- Tipologia solaio: prestressed_slab
- Anno costruzione: 1980

CONSIDERAZIONI SPECIFICHE PER PREFABBRICATI:
- Utilizzare parametri per cemento armato precompresso
- Considerare collegamenti tra elementi prefabbricati
- Valutare rigidezza del sistema strutturale
- Formula periodo: T = 0.070 × H^0.75 per precompresso

PIANI:
- Piano 1: Area 1000 m², Altezza 5 m, Uso: commercial

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-16 12:47:26] RICHIESTA AI - Session: ugev8bl2gptglrrme4g3n76lt0 - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.807919,
        "lon": 12.679396
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 1980,
        "floors": [
            {
                "level": 1,
                "area": 400,
                "height": 3,
                "use": "residential"
            },
            {
                "level": 2,
                "area": 400,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.807919, 12.679396
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: hollow_brick
- Anno costruzione: 1980

PIANI:
- Piano 1: Area 400 m², Altezza 3 m, Uso: residential
- Piano 2: Area 400 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}

================================================================================
[2025-06-16 12:54:07] RICHIESTA AI - Session: ugev8bl2gptglrrme4g3n76lt0 - User: 3
================================================================================

--- DATI INPUT ---
{
    "location": {
        "lat": 41.808087,
        "lon": 12.679358
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.17,
        "F0": 2.586,
        "TC": 0.279,
        "soil_category": "C",
        "damping": 5,
        "q_factor": 1
    },
    "building": {
        "construction_category": "building",
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 2000,
        "floors": [
            {
                "level": 1,
                "area": 2000,
                "height": 3,
                "use": "residential"
            }
        ]
    }
}

--- CONFIGURAZIONE API ---
{
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "deepseek_api_key": "[NASCOSTA]",
    "deepseek_model": "deepseek-chat",
    "deepseek_api_url": "https:\/\/api.deepseek.com\/v1\/chat\/completions",
    "gemini_api_key": "AIzaSyD0dOSb27FSSkivO5OmDgIwfPzC8Uvwovo",
    "gemini_model": "gemma-2-2b-it",
    "gemini_api_url": "https:\/\/generativelanguage.googleapis.com\/v1beta\/models\/",
    "api_timeout": 120,
    "max_retries": 3,
    "retry_delay": 2,
    "enable_cache": true,
    "cache_ttl": 3600,
    "rate_limit": 10,
    "require_auth": true
}

--- PROMPT GENERATO ---
Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. Calcola la massa inerziale e le forze sismiche per il seguente edificio:

DATI SISMICI:
- Coordinate: 41.808087, 12.679358
- Zona sismica: 3
- ag: 0.17 g
- F0: 2.586
- TC*: 0.279 s
- Categoria sottosuolo: C
- Smorzamento: 5%

DATI STRUTTURA:
- Categoria costruttiva: Building
- Tipologia strutturale: concrete
- Tipologia solaio: hollow_brick
- Anno costruzione: 2000

PIANI:
- Piano 1: Area 2000 m², Altezza 3 m, Uso: residential

Calcola secondo NTC 2018:
1. Peso per unità di superficie per ogni piano (kN/m²)
2. Massa totale dell'edificio (tonnellate)
3. Periodo fondamentale T1 (secondi)
4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = 5%
5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato
6. Distribuzione delle forze per piano (kN)
7. Breve analisi della vulnerabilità sismica

Restituisci SOLO un JSON con questa struttura:
```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```

--- PARAMETRI CHIAMATA API ---
{
    "model": "deepseek-chat",
    "temperature": 0.2,
    "max_tokens": 2000,
    "stream": false
}
