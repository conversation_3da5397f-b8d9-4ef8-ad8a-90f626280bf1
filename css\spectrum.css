/* css/spectrum.css */
/* <PERSON>ili per il modulo Spettri di Risposta */
/* Integrazione con design system ASDP esistente */
/* Versione: 1.0 - Data: 15/06/2025 */

/* =====================================================
   CONTAINER PRINCIPALE SPETTRI
   ===================================================== */

.spectrum-visualizer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #f8f9fa;
}

/* =====================================================
   VISUALIZZAZIONE CONFRONTO SOVRAPPOSTO
   ===================================================== */

.spectrum-comparison-container {
    background: linear-gradient(135deg, #2d2d2d 0%, #3a3a3a 100%);
    border-radius: 12px;
    padding: 2rem;
    margin: 1.5rem 0;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.15);
}

.spectrum-comparison-title {
    color: #3498db;
    font-size: 1.4rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.spectrum-comparison-legend {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.legend-color-ante {
    width: 20px;
    height: 4px;
    background: #e74c3c;
    border-radius: 2px;
}

.legend-color-post {
    width: 20px;
    height: 4px;
    background: #27ae60;
    border-radius: 2px;
}

/* =====================================================
   METADATI CONFRONTO SPETTRI
   ===================================================== */

.comparison-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
    padding: 1.5rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.stat-item.highlight {
    border-left-color: #f39c12;
    background: rgba(243, 156, 18, 0.1);
}

.stat-label {
    font-weight: 600;
    color: #bdc3c7;
    font-size: 0.9rem;
}

.stat-value {
    font-weight: 700;
    color: #ecf0f1;
    font-size: 1rem;
    font-family: 'Courier New', monospace;
}

.comparison-note {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(39, 174, 96, 0.1);
    border-left: 4px solid #27ae60;
    border-radius: 4px;
    color: #27ae60;
    font-weight: 600;
}

/* =====================================================
   CONTROLLI SPETTRI
   ===================================================== */

.spectrum-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 600;
    color: #e9ecef;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.control-group .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 0.75rem;
    color: #f8f9fa;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.control-group .form-control:focus {
    outline: none;
    border-color: #D97706;
    box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.2);
    background: rgba(255, 255, 255, 0.15);
}

.control-group .form-control:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(255, 255, 255, 0.05);
}

/* Pulsanti controlli */
.control-group .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.control-group .btn-primary {
    background: linear-gradient(135deg, #D97706 0%, #C26A05 100%);
    color: white;
}

.control-group .btn-primary:hover {
    background: linear-gradient(135deg, #C26A05 0%, #B45309 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(217, 119, 6, 0.3);
}

.control-group .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
}

.control-group .btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* =====================================================
   CONTAINER GRAFICO
   ===================================================== */

.spectrum-chart-container {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    height: 500px;
}

.spectrum-chart-container canvas {
    max-width: 100%;
    height: 100% !important;
}

/* =====================================================
   PANNELLI INFORMAZIONI
   ===================================================== */

.spectrum-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.metadata-panel,
.values-panel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.metadata-panel h4,
.values-panel h4 {
    color: #D97706;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 2px solid rgba(217, 119, 6, 0.3);
    padding-bottom: 0.5rem;
}

/* =====================================================
   GRIGLIA METADATI
   ===================================================== */

.metadata-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.param-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border-left: 3px solid #D97706;
}

.param-label {
    font-size: 0.8rem;
    color: #adb5bd;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.param-value {
    font-size: 1rem;
    color: #f8f9fa;
    font-weight: 700;
}

/* =====================================================
   STATISTICHE CONFRONTO
   ===================================================== */

.comparison-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.stat-item.highlight {
    border-color: #27ae60;
    background: rgba(39, 174, 96, 0.1);
}

.stat-label {
    font-size: 0.85rem;
    color: #adb5bd;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.stat-value {
    font-size: 1.5rem;
    color: #f8f9fa;
    font-weight: 700;
}

.stat-item.highlight .stat-value {
    color: #27ae60;
}

/* =====================================================
   RESPONSIVE DESIGN
   ===================================================== */

@media (max-width: 768px) {
    .spectrum-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .spectrum-info {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .metadata-grid {
        grid-template-columns: 1fr;
    }
    
    .comparison-stats {
        grid-template-columns: 1fr;
    }
    
    .spectrum-chart-container {
        height: 400px;
        padding: 1rem;
    }
    
    .spectrum-visualizer {
        padding: 1rem;
        margin: 0.5rem 0;
    }
}

@media (max-width: 480px) {
    .spectrum-chart-container {
        height: 300px;
        padding: 0.75rem;
    }
    
    .control-group .btn {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }
    
    .param-item,
    .stat-item {
        padding: 0.75rem;
    }
    
    .stat-value {
        font-size: 1.25rem;
    }
}

/* =====================================================
   ANIMAZIONI E TRANSIZIONI
   ===================================================== */

.spectrum-visualizer {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.param-item,
.stat-item {
    animation: slideInLeft 0.4s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* =====================================================
   UTILITÀ E HELPER
   ===================================================== */

.spectrum-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #adb5bd;
    font-size: 1.1rem;
}

.spectrum-error {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #f8d7da;
    padding: 1rem;
    border-radius: 6px;
    margin: 1rem 0;
}

.spectrum-success {
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: #d4edda;
    padding: 1rem;
    border-radius: 6px;
    margin: 1rem 0;
}

/* =====================================================
   INTEGRAZIONE CON MODAL MASSA INERZIALE
   ===================================================== */

.inertial-mass-modal .spectrum-visualizer {
    margin: 0;
    border-radius: 0;
    background: transparent;
    box-shadow: none;
    border: none;
    padding: 0;
}

.inertial-mass-modal .spectrum-controls {
    background: rgba(217, 119, 6, 0.1);
    border-color: rgba(217, 119, 6, 0.3);
}

.inertial-mass-modal .spectrum-chart-container {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}
