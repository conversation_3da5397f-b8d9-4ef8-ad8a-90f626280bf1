<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Integrazione Spettri nel Modal - ASDP v2.5.1</title>
    <link rel="stylesheet" href="css/spectrum.css">
    <link rel="stylesheet" href="inertial_mass/assets/css/modal.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="js/SpectrumVisualizerModal.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #f8f9fa;
            margin: 0;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: #2d2d2d;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #3498db;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            border-left: 4px solid #f39c12;
        }
        
        .results-section {
            background: #1E1E1E;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .results-summary {
            margin-bottom: 2rem;
        }
        
        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            margin: 0.5rem 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            border-left: 4px solid #3498db;
        }
        
        .result-label {
            font-weight: 600;
            color: #bdc3c7;
        }
        
        .result-value {
            font-weight: 700;
            color: #ecf0f1;
            font-family: 'Courier New', monospace;
        }
        
        .spectrum-comparison-container {
            background: #2d2d2d;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .spectrum-chart-container {
            height: 400px;
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: background 0.3s ease;
        }
        
        .test-button:hover {
            background: #2980b9;
        }
        
        .success-message {
            background: rgba(39, 174, 96, 0.2);
            border: 1px solid #27ae60;
            color: #27ae60;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        
        .format-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .format-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid #e74c3c;
        }
        
        .format-label {
            font-weight: 600;
            color: #bdc3c7;
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .format-value {
            font-weight: 700;
            color: #ecf0f1;
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 Test Integrazione Spettri nel Modal</h1>
            <p>Test delle modifiche v2.5.1: formattazione 4 decimali e grafico confronto nei risultati</p>
        </div>

        <div class="test-section">
            <h2>🔢 Test 1: Formattazione 4 Decimali</h2>
            <p>Verifica che tutti i valori numerici utilizzino esattamente 4 cifre decimali</p>
            
            <div class="format-test">
                <div class="format-item">
                    <span class="format-label">Massa Totale</span>
                    <span class="format-value" id="test-mass">1234.5678 t</span>
                </div>
                <div class="format-item">
                    <span class="format-label">Periodo Fondamentale</span>
                    <span class="format-value" id="test-period">0.2680 s</span>
                </div>
                <div class="format-item">
                    <span class="format-label">Forza Sismica</span>
                    <span class="format-value" id="test-force">987.6543 kN</span>
                </div>
                <div class="format-item">
                    <span class="format-label">Smorzamento ANTE</span>
                    <span class="format-value" id="test-damping-before">5.0000%</span>
                </div>
                <div class="format-item">
                    <span class="format-label">Smorzamento POST</span>
                    <span class="format-value" id="test-damping-after">12.3456%</span>
                </div>
                <div class="format-item">
                    <span class="format-label">Riduzione Media</span>
                    <span class="format-value" id="test-reduction">22.7890%</span>
                </div>
            </div>
            
            <button class="test-button" onclick="testNumberFormatting()">🧮 Test Formattazione</button>
            <div id="format-results"></div>
        </div>

        <div class="test-section">
            <h2>📊 Test 2: Grafico Confronto nei Risultati</h2>
            <p>Simulazione della sezione risultati con grafico di confronto integrato</p>
            
            <div class="results-section">
                <div class="results-summary">
                    <h4>📋 Riepilogo Calcolo</h4>
                    <div class="result-item">
                        <span class="result-label">💪 Massa Totale:</span>
                        <span class="result-value">1234.5678 t</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">⏱️ Periodo Fondamentale:</span>
                        <span class="result-value">0.2680 s</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">⚡ Forza Sismica Totale:</span>
                        <span class="result-value">987.6543 kN</span>
                    </div>
                </div>

                <div class="results-spectrum-comparison">
                    <h4>📊 Confronto Spettri di Risposta ANTE/POST</h4>
                    <div class="spectrum-comparison-container">
                        <div class="spectrum-chart-container">
                            <canvas id="test-spectrum-comparison-chart"></canvas>
                        </div>
                        <div id="test-spectrum-metadata" style="margin-top: 1rem; padding: 1rem; background: rgba(0, 0, 0, 0.2); border-radius: 8px;">
                            <p style="color: #bdc3c7; text-align: center; margin: 0;">
                                <i class="fas fa-spinner fa-spin"></i> Caricamento confronto spettri...
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <button class="test-button" onclick="testSpectrumIntegration()">📈 Test Grafico Confronto</button>
            <div id="spectrum-results"></div>
        </div>

        <div class="test-section">
            <h2>🎯 Test 3: Compatibilità Modal 1400px</h2>
            <p>Verifica che il grafico si adatti correttamente al modal ingrandito</p>
            
            <div style="width: 1400px; max-width: 100%; margin: 0 auto; background: #1E1E1E; border-radius: 8px; padding: 1rem;">
                <h5 style="color: #3498db; text-align: center;">Simulazione Modal 1400px</h5>
                <div class="spectrum-chart-container" style="height: 350px;">
                    <canvas id="test-modal-chart"></canvas>
                </div>
            </div>
            
            <button class="test-button" onclick="testModalCompatibility()">📐 Test Compatibilità Modal</button>
            <div id="modal-results"></div>
        </div>

        <div class="test-section">
            <h2>📋 Risultati Test</h2>
            <div id="test-summary">
                <p>Esegui i test per vedere i risultati...</p>
            </div>
        </div>
    </div>

    <script>
        // Dati di test sintetici
        const testDataBefore = {
            periods: [0.0000, 0.1000, 0.2000, 0.3000, 0.4000, 0.5000, 0.6000, 0.7000, 0.8000, 0.9000, 1.0000],
            accelerations: [0.0000, 0.3456, 0.5678, 0.4321, 0.3789, 0.3234, 0.2890, 0.2567, 0.2345, 0.2123, 0.1987]
        };

        const testDataAfter = {
            periods: [0.0000, 0.1000, 0.2000, 0.3000, 0.4000, 0.5000, 0.6000, 0.7000, 0.8000, 0.9000, 1.0000],
            accelerations: [0.0000, 0.2678, 0.4321, 0.3234, 0.2890, 0.2456, 0.2123, 0.1890, 0.1678, 0.1456, 0.1234]
        };

        let testCharts = {};

        function testNumberFormatting() {
            console.log('🧮 Test formattazione numerica a 4 decimali...');
            
            const testValues = [
                { id: 'test-mass', value: 1234.56789123, unit: 't' },
                { id: 'test-period', value: 0.26799999, unit: 's' },
                { id: 'test-force', value: 987.65432109, unit: 'kN' },
                { id: 'test-damping-before', value: 5.0, unit: '%' },
                { id: 'test-damping-after', value: 12.345678, unit: '%' },
                { id: 'test-reduction', value: 22.789012, unit: '%' }
            ];

            let allCorrect = true;
            const results = [];

            testValues.forEach(test => {
                const element = document.getElementById(test.id);
                const formatted = parseFloat(test.value).toFixed(4);
                element.textContent = `${formatted} ${test.unit}`;
                
                const decimals = formatted.split('.')[1];
                const isCorrect = decimals && decimals.length === 4;
                
                results.push({
                    label: test.id,
                    original: test.value,
                    formatted: formatted,
                    correct: isCorrect
                });
                
                if (!isCorrect) allCorrect = false;
            });

            const resultsDiv = document.getElementById('format-results');
            resultsDiv.innerHTML = `
                <div class="success-message">
                    ${allCorrect ? '✅' : '❌'} Test formattazione: ${allCorrect ? 'SUCCESSO' : 'FALLITO'}
                    <br>Tutti i valori utilizzano esattamente 4 cifre decimali: ${allCorrect ? 'SÌ' : 'NO'}
                </div>
            `;

            updateTestSummary('Formattazione 4 decimali', allCorrect);
        }

        function testSpectrumIntegration() {
            console.log('📈 Test integrazione grafico confronto...');
            
            try {
                const ctx = document.getElementById('test-spectrum-comparison-chart').getContext('2d');
                
                if (testCharts.comparison) {
                    testCharts.comparison.destroy();
                }

                testCharts.comparison = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: testDataBefore.periods.map(p => p.toFixed(4)),
                        datasets: [
                            {
                                label: 'ANTE Intervento (ξ=5.0000%)',
                                data: testDataBefore.accelerations.map(a => parseFloat(a.toFixed(4))),
                                borderColor: '#e74c3c',
                                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                                borderWidth: 3,
                                fill: false,
                                tension: 0.4
                            },
                            {
                                label: 'POST Intervento (ξ=12.3456%)',
                                data: testDataAfter.accelerations.map(a => parseFloat(a.toFixed(4))),
                                borderColor: '#27ae60',
                                backgroundColor: 'rgba(39, 174, 96, 0.1)',
                                borderWidth: 3,
                                fill: false,
                                tension: 0.4
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Confronto Spettri ANTE/POST Dissipatori',
                                font: { size: 16, weight: 'bold' }
                            },
                            legend: { display: true, position: 'top' },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `${context.dataset.label}: ${context.parsed.y.toFixed(4)} g`;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Periodo T (s)' } },
                            y: { title: { display: true, text: 'Accelerazione Se (g)' }, beginAtZero: true }
                        }
                    }
                });

                // Aggiorna metadati
                document.getElementById('test-spectrum-metadata').innerHTML = `
                    <h5 style="color: #3498db; margin: 0 0 1rem 0; text-align: center;">
                        <i class="fas fa-chart-bar"></i> Analisi Efficacia Dissipatori
                    </h5>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 6px; border-left: 4px solid #e74c3c;">
                            <span style="font-weight: 600; color: #bdc3c7; display: block; margin-bottom: 0.5rem;">🔴 Smorzamento ANTE</span>
                            <span style="font-weight: 700; color: #ecf0f1; font-family: 'Courier New', monospace; font-size: 1.1rem;">5.0000%</span>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 6px; border-left: 4px solid #27ae60;">
                            <span style="font-weight: 600; color: #bdc3c7; display: block; margin-bottom: 0.5rem;">🟢 Smorzamento POST</span>
                            <span style="font-weight: 700; color: #ecf0f1; font-family: 'Courier New', monospace; font-size: 1.1rem;">12.3456%</span>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 6px; border-left: 4px solid #f39c12;">
                            <span style="font-weight: 600; color: #bdc3c7; display: block; margin-bottom: 0.5rem;">📉 Riduzione Media</span>
                            <span style="font-weight: 700; color: #ecf0f1; font-family: 'Courier New', monospace; font-size: 1.1rem;">22.7890%</span>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 6px; border-left: 4px solid #9b59b6;">
                            <span style="font-weight: 600; color: #bdc3c7; display: block; margin-bottom: 0.5rem;">⚡ Efficienza Sistema</span>
                            <span style="font-weight: 700; color: #ecf0f1; font-family: 'Courier New', monospace; font-size: 1.1rem;">85.4321%</span>
                        </div>
                    </div>
                `;

                document.getElementById('spectrum-results').innerHTML = `
                    <div class="success-message">
                        ✅ Test integrazione grafico: SUCCESSO
                        <br>Grafico confronto ANTE/POST integrato correttamente nei risultati
                    </div>
                `;

                updateTestSummary('Integrazione grafico confronto', true);

            } catch (error) {
                console.error('Errore test grafico:', error);
                document.getElementById('spectrum-results').innerHTML = `
                    <div style="background: rgba(231, 76, 60, 0.2); border: 1px solid #e74c3c; color: #e74c3c; padding: 1rem; border-radius: 6px;">
                        ❌ Test integrazione grafico: FALLITO
                        <br>Errore: ${error.message}
                    </div>
                `;
                updateTestSummary('Integrazione grafico confronto', false);
            }
        }

        function testModalCompatibility() {
            console.log('📐 Test compatibilità modal 1400px...');
            
            try {
                const ctx = document.getElementById('test-modal-chart').getContext('2d');
                
                if (testCharts.modal) {
                    testCharts.modal.destroy();
                }

                testCharts.modal = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: testDataBefore.periods.map(p => p.toFixed(4)),
                        datasets: [
                            {
                                label: 'ANTE (ξ=5.0000%)',
                                data: testDataBefore.accelerations.map(a => parseFloat(a.toFixed(4))),
                                borderColor: '#e74c3c',
                                borderWidth: 2,
                                fill: false
                            },
                            {
                                label: 'POST (ξ=12.3456%)',
                                data: testDataAfter.accelerations.map(a => parseFloat(a.toFixed(4))),
                                borderColor: '#27ae60',
                                borderWidth: 2,
                                fill: false
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: { display: true, text: 'Test Modal 1400px' },
                            legend: { display: true }
                        }
                    }
                });

                document.getElementById('modal-results').innerHTML = `
                    <div class="success-message">
                        ✅ Test compatibilità modal: SUCCESSO
                        <br>Grafico si adatta correttamente al modal 1400px
                    </div>
                `;

                updateTestSummary('Compatibilità modal 1400px', true);

            } catch (error) {
                console.error('Errore test modal:', error);
                updateTestSummary('Compatibilità modal 1400px', false);
            }
        }

        function updateTestSummary(testName, success) {
            const summaryDiv = document.getElementById('test-summary');
            const timestamp = new Date().toLocaleTimeString();
            
            const currentContent = summaryDiv.innerHTML;
            const newEntry = `<div class="success-message">[${timestamp}] ${testName}: ${success ? '✅ SUCCESSO' : '❌ FALLITO'}</div>`;
            
            if (currentContent.includes('Esegui i test')) {
                summaryDiv.innerHTML = newEntry;
            } else {
                summaryDiv.innerHTML = currentContent + newEntry;
            }
        }

        // Inizializza i test al caricamento
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Test inizializzati');
            updateTestSummary('Sistema', true);
        });
    </script>
</body>
</html>
