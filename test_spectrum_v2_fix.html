<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test SpectrumVisualizerModal v2 - Fix Definitivo Canvas</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="js/SpectrumVisualizerModal_v2.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #f8f9fa;
            margin: 0;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: #2d2d2d;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #27ae60;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }
        
        .canvas-container {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            height: 300px;
        }
        
        .test-button {
            background: #27ae60;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: background 0.3s ease;
        }
        
        .test-button:hover {
            background: #229954;
        }
        
        .test-button.stress {
            background: #e74c3c;
        }
        
        .test-button.stress:hover {
            background: #c0392b;
        }
        
        .success-message {
            background: rgba(39, 174, 96, 0.2);
            border: 1px solid #27ae60;
            color: #27ae60;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        
        .error-message {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid #e74c3c;
            color: #e74c3c;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        
        .log-container {
            background: #1a1a1a;
            border: 1px solid #444;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-entry {
            margin: 0.25rem 0;
            padding: 0.25rem;
        }
        
        .log-success {
            color: #27ae60;
        }
        
        .log-error {
            color: #e74c3c;
        }
        
        .log-info {
            color: #3498db;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #27ae60;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #bdc3c7;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 Test SpectrumVisualizerModal v2</h1>
            <p>Test della versione migliorata con pulizia robusta canvas</p>
        </div>

        <div class="test-section">
            <h2>📊 Test Rapido: Canvas Multipli</h2>
            <p>Test veloce di creazione simultanea su canvas diversi</p>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div class="canvas-container">
                    <h4>Canvas A</h4>
                    <canvas id="canvas-a" width="400" height="200"></canvas>
                </div>
                <div class="canvas-container">
                    <h4>Canvas B</h4>
                    <canvas id="canvas-b" width="400" height="200"></canvas>
                </div>
            </div>
            
            <button class="test-button" onclick="testQuickMultiple()">⚡ Test Rapido</button>
            <div id="quick-results"></div>
        </div>

        <div class="test-section">
            <h2>🔄 Test Stress: Riutilizzo Intensivo</h2>
            <p>Test di stress con riutilizzo rapido dello stesso canvas</p>
            
            <div class="canvas-container">
                <h4>Canvas Stress Test</h4>
                <canvas id="stress-canvas" width="400" height="200"></canvas>
            </div>
            
            <button class="test-button stress" onclick="testStressReuse()">💥 Test Stress (10x)</button>
            <button class="test-button" onclick="stopStressTest()">⏹️ Stop</button>
            <div id="stress-results"></div>
        </div>

        <div class="test-section">
            <h2>📈 Statistiche Test</h2>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value" id="success-count">0</div>
                    <div class="stat-label">Successi</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="error-count">0</div>
                    <div class="stat-label">Errori</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="total-count">0</div>
                    <div class="stat-label">Totale</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="success-rate">0%</div>
                    <div class="stat-label">Tasso Successo</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 Log Test</h2>
            <div class="log-container" id="test-log">
                <div class="log-entry log-info">SpectrumVisualizerModal v2 pronto per i test...</div>
            </div>
            <button class="test-button" onclick="clearLog()">🗑️ Pulisci Log</button>
        </div>
    </div>

    <script>
        // Statistiche test
        let stats = {
            success: 0,
            error: 0,
            total: 0
        };

        let stressTestRunning = false;

        // Dati di test
        const testData = {
            labels: ['0.0', '0.1', '0.2', '0.3', '0.4', '0.5'],
            datasets: [{
                label: 'Test Data',
                data: [0, 0.3, 0.5, 0.4, 0.3, 0.2],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                borderWidth: 2,
                fill: false
            }]
        };

        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: { display: true, text: 'Test Chart v2' },
                legend: { display: true }
            },
            scales: {
                x: { title: { display: true, text: 'Periodo (s)' } },
                y: { title: { display: true, text: 'Accelerazione (g)' }, beginAtZero: true }
            }
        };

        function log(message, type = 'info') {
            const logContainer = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '<div class="log-entry log-info">Log pulito...</div>';
        }

        function updateStats(success) {
            stats.total++;
            if (success) {
                stats.success++;
            } else {
                stats.error++;
            }

            document.getElementById('success-count').textContent = stats.success;
            document.getElementById('error-count').textContent = stats.error;
            document.getElementById('total-count').textContent = stats.total;
            
            const successRate = stats.total > 0 ? Math.round((stats.success / stats.total) * 100) : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
        }

        function testQuickMultiple() {
            log('Iniziando test rapido canvas multipli...', 'info');
            
            try {
                // Canvas A
                log('Creando grafico su canvas-a...', 'info');
                const visualizerA = new SpectrumVisualizerModal('canvas-a');
                
                const dataA = {
                    ...testData,
                    datasets: [{
                        ...testData.datasets[0],
                        label: 'Canvas A',
                        borderColor: '#e74c3c'
                    }]
                };
                
                visualizerA.createChart(dataA, chartOptions);
                log('Canvas A: SUCCESSO', 'success');
                updateStats(true);

                // Canvas B
                setTimeout(() => {
                    try {
                        log('Creando grafico su canvas-b...', 'info');
                        const visualizerB = new SpectrumVisualizerModal('canvas-b');
                        
                        const dataB = {
                            ...testData,
                            datasets: [{
                                ...testData.datasets[0],
                                label: 'Canvas B',
                                borderColor: '#27ae60'
                            }]
                        };
                        
                        visualizerB.createChart(dataB, chartOptions);
                        log('Canvas B: SUCCESSO', 'success');
                        updateStats(true);

                        document.getElementById('quick-results').innerHTML = `
                            <div class="success-message">
                                ✅ Test rapido: SUCCESSO COMPLETO
                                <br>Entrambi i canvas funzionano perfettamente
                            </div>
                        `;
                    } catch (errorB) {
                        log(`Canvas B: ERRORE - ${errorB.message}`, 'error');
                        updateStats(false);
                        document.getElementById('quick-results').innerHTML = `
                            <div class="error-message">
                                ❌ Test rapido: FALLITO su Canvas B
                                <br>Errore: ${errorB.message}
                            </div>
                        `;
                    }
                }, 100);

            } catch (errorA) {
                log(`Canvas A: ERRORE - ${errorA.message}`, 'error');
                updateStats(false);
                document.getElementById('quick-results').innerHTML = `
                    <div class="error-message">
                        ❌ Test rapido: FALLITO su Canvas A
                        <br>Errore: ${errorA.message}
                    </div>
                `;
            }
        }

        function testStressReuse() {
            if (stressTestRunning) {
                log('Test stress già in corso...', 'info');
                return;
            }

            stressTestRunning = true;
            log('Iniziando test stress riutilizzo canvas...', 'info');
            
            let iteration = 0;
            const maxIterations = 10;
            
            function runIteration() {
                if (!stressTestRunning || iteration >= maxIterations) {
                    stressTestRunning = false;
                    log(`Test stress completato: ${iteration} iterazioni`, 'success');
                    
                    const successRate = stats.total > 0 ? Math.round((stats.success / stats.total) * 100) : 0;
                    document.getElementById('stress-results').innerHTML = `
                        <div class="success-message">
                            ✅ Test stress completato
                            <br>Iterazioni: ${iteration}/${maxIterations}
                            <br>Tasso successo: ${successRate}%
                        </div>
                    `;
                    return;
                }

                iteration++;
                log(`Iterazione stress ${iteration}/${maxIterations}...`, 'info');
                
                try {
                    const visualizer = new SpectrumVisualizerModal('stress-canvas');
                    
                    const data = {
                        ...testData,
                        datasets: [{
                            ...testData.datasets[0],
                            label: `Stress ${iteration}`,
                            borderColor: `hsl(${iteration * 36}, 70%, 50%)`
                        }]
                    };
                    
                    visualizer.createChart(data, chartOptions);
                    log(`Iterazione ${iteration}: SUCCESSO`, 'success');
                    updateStats(true);
                    
                    // Prossima iterazione
                    setTimeout(runIteration, 200);
                    
                } catch (error) {
                    log(`Iterazione ${iteration}: ERRORE - ${error.message}`, 'error');
                    updateStats(false);
                    
                    // Continua comunque
                    setTimeout(runIteration, 200);
                }
            }
            
            runIteration();
        }

        function stopStressTest() {
            if (stressTestRunning) {
                stressTestRunning = false;
                log('Test stress fermato dall\'utente', 'info');
            }
        }

        // Inizializzazione
        document.addEventListener('DOMContentLoaded', function() {
            log('SpectrumVisualizerModal v2 inizializzato e pronto', 'success');
        });
    </script>
</body>
</html>
