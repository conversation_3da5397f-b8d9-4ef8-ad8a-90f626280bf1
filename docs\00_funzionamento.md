# Funzionamento dell'Applicazione ASDP v2.5.1

## Panoramica
ASDP (Advanced Seismic Dissipator Project) è un'applicazione web avanzata per il calcolo e l'analisi dei parametri sismici con integrazione AI. L'applicazione permette di:
- Ricercare località tramite mappa interattiva Google Maps
- Calcolare parametri sismici secondo NTC 2018
- **Calcolare massa inerziale sismica con AI** (Modulo v2.5.0)
- **Raccomandazioni dissipatori sismici automatiche**
- Generare report tecnici professionali
- Sistema di backup e gestione dati avanzato

## Accesso al Sistema

### Login
1. Accedere alla pagina di login (`login.php`)
2. Inserire credenziali (username/email e password)
3. Il sistema verifica le credenziali e reindirizza alla dashboard

### Registrazione
1. Dalla pagina di login, cliccare su "Registrati"
2. Compilare il form con:
   - Username
   - Email
   - Password
   - Conferma password
3. Il sistema verifica i dati e crea il nuovo account

## Interfaccia Principale

### Dashboard
- Mappa interattiva per la selezione del punto
- Pannello informazioni località
- Barra laterale con menu di navigazione
- Area calcoli parametri sismici

### Menu Laterale
1. **Dashboard** ✅ **ATTIVO**
   - Panoramica generale progetti
   - Mappa interattiva Google Maps
   - Calcolo parametri sismici NTC 2018
   - **Modulo Massa Inerziale integrato**

2. **Nuova Valutazione** (in sviluppo)
   - Form per nuova valutazione guidata
   - Selezione parametri avanzati
   - Calcolo risultati automatico

3. **Storico Valutazioni** (in sviluppo)
   - Lista valutazioni precedenti
   - Filtri e ricerca avanzata
   - Dettagli calcoli storici

4. **Report** ✅ **ATTIVO**
   - Generazione report HTML professionali
   - Template ottimizzati per stampa
   - Esportazione dati tecnici
   - **Report massa inerziale con AI**

5. **Progetti** (in sviluppo)
   - Gestione progetti strutturali
   - Organizzazione valutazioni
   - Condivisione dati team

6. **Account** ✅ **ATTIVO**
   - Gestione profilo utente
   - Configurazione account
   - Preferenze sistema

7. **Impostazioni** ✅ **ATTIVO**
   - Configurazione applicazione
   - Gestione backup automatici
   - Impostazioni avanzate sistema

## Funzionalità Core

### 1. Ricerca Località ✅ **COMPLETA**
- Ricerca per indirizzo/coordinate con Google Maps
- Selezione punto interattiva sulla mappa
- Visualizzazione dati catastali automatica
- Info amministrative complete (comune, provincia, regione)
- Geocoding inverso per coordinate precise

### 2. Calcolo Parametri Sismici ✅ **COMPLETA**
- **Input parametri di calcolo:**
  - Vita nominale (VN)
  - Classe d'uso (CU)
  - Categoria sottosuolo (A, B, C, D, E)
  - Categoria topografica (T1, T2, T3, T4)
  - Smorzamento viscoso (%)
  - Fattore di struttura (q)
- **Calcolo automatico parametri NTC 2018:**
  - ag [g] - Accelerazione al suolo
  - F0 - Fattore di amplificazione spettrale
  - Tc* [s] - Periodo di riferimento
  - TR [anni] - Periodo di ritorno dinamico

### 3. **Modulo Massa Inerziale** ✅ **NUOVO v2.5.0**
- **Calcolo automatico massa inerziale sismica**
- **Integrazione AI con sistema a 3 livelli:**
  - Livello 1: Google Gemma3 (primario)
  - Livello 2: Deepseek AI (fallback)
  - Livello 3: Calcolo locale NTC 2018 (garantito)
- **Tipologie costruttive supportate:**
  - Ponti/Viadotti (c.a. precompresso)
  - Edifici generici (tutte le tipologie)
  - Edifici prefabbricati (c.a. precompresso)
- **Raccomandazioni dissipatori automatiche**
- **Interfaccia modale responsive 1400px**

### 4. Generazione Report ✅ **COMPLETA**
- **Report HTML professionali** con template ottimizzati
- **5 sezioni strutturate:**
  - Dati di input geografici e sismici
  - Risultati calcolo massa inerziale
  - Raccomandazioni dissipatori sismici
  - Analisi AI avanzata
  - Note tecniche e conclusioni
- **Funzionalità stampa** ottimizzata
- **Verifica utilizzo dati reali** (no hardcoded)

## Gestione Dati

### Database ✅ **OTTIMIZZATO**
- **Tabelle principali:**
  - `users` - Gestione utenti e autenticazione
  - `seismic_calculations` - Storico calcoli sismici
  - `inertial_mass_calculations` - Calcoli massa inerziale
  - `inertial_mass_floors` - Dati piani edifici
  - `inertial_mass_dampers` - Raccomandazioni dissipatori
  - `inertial_mass_results` - Risultati AI e analisi
- **Configurazioni sistema** centralizzate
- **Log sistema** completo con rotazione automatica

### Sistema Backup ✅ **AVANZATO**
- **Backup automatico database** con scheduling
- **Backup file configurazione** e assets
- **Gestione versioni** con timestamp
- **Restore dati** con interfaccia admin
- **Backup ZIP** ottimizzato per struttura pulita
- **Fallback multipli** per robustezza sistema

## Sicurezza ✅ **ROBUSTA**

### Autenticazione
- **Login sicuro** con hash password bcrypt
- **Gestione sessioni** PHP sicure
- **Protezione CSRF** su tutti i form
- **Rate limiting** per API LLM
- **Timeout automatici** per sicurezza

### Validazione e Protezione
- **Input sanitization** completa
- **Controllo permessi** granulare
- **Validazione dati** lato client e server
- **Logging accessi** dettagliato
- **Protezione SQL injection**
- **XSS prevention** su output dinamico

## Note Tecniche

### Requisiti Sistema ✅ **VERIFICATI**
- **XAMPP v3.3.0+** (testato su Windows 11)
- **PHP 8.2.12+** con estensioni: curl, json, zip
- **MySQL 8.0+** con InnoDB
- **Browser moderno** con JavaScript ES6+
- **Connessione internet** per API Google Maps e LLM

### Performance ✅ **OTTIMIZZATE**
- **Caching intelligente** risultati calcoli (1 ora)
- **Ottimizzazione query** database con indici
- **Compressione assets** JS/CSS minificati
- **Lazy loading** componenti pesanti
- **Sistema cache** a doppio livello per versioning

### Architettura Sistema
- **Pattern MVC** per organizzazione codice
- **Singleton pattern** per manager centralizzati
- **API RESTful** per servizi esterni
- **Responsive design** mobile-first
- **Progressive enhancement** per accessibilità

### Manutenzione ✅ **AUTOMATIZZATA**
- **Log errori** in `/logs` con rotazione automatica
- **Backup periodici** database e file
- **Monitoraggio sistema** con logging dettagliato
- **Aggiornamenti versione** automatici
- **Pulizia cache** programmata

## Integrazione AI ✅ **AVANZATA**

### Sistema a Tre Livelli LLM
- **Livello 1: Deepseek AI** - Analisi ingegneristica avanzata
- **Livello 2: Google Gemma3** - Modello compatto e veloce
- **Livello 3: Calcolo Locale** - Formule NTC 2018 garantite
- **Fallback automatico** trasparente tra provider
- **Affidabilità 99.9%** garantita

### Funzionalità AI
- **Analisi strutturale automatica** con AI
- **Raccomandazioni dissipatori** ottimizzate
- **Spiegazioni tecniche** generate dinamicamente
- **Validazione risultati** con logica ingegneristica

## Documentazione ✅ **COMPLETA**

### File Documentazione (cartella `/docs`)
- **00_funzionamento.md** - Guida funzionamento (questo file)
- **11_miglioramenti.md** - Registro miglioramenti cronologico
- **12_aggiornamenti.md** - Changelog versioni dettagliato
- **app_map.md** - Mappa completa struttura progetto
- **verifica_dati_reali_v2.5.0.md** - Verifica conformità sistema

### Supporto Tecnico
- **Documentazione tecnica** completa e aggiornata
- **Troubleshooting** guidato per problemi comuni
- **Test automatici** per validazione funzionalità
- **Logging dettagliato** per debug avanzato

---

## 🎯 **Stato Attuale ASDP v2.5.0**

### ✅ **Funzionalità Completate**
- Sistema calcolo parametri sismici NTC 2018
- Modulo massa inerziale con AI integrata
- Sistema backup e gestione dati
- Generazione report professionali
- Interfaccia utente responsive
- Documentazione completa

### 🔄 **In Sviluppo**
- Dashboard personalizzata avanzata
- Nuova valutazione guidata
- Storico valutazioni con filtri
- Gestione progetti multipli
- Sistema notifiche

### 🚀 **Prossimi Sviluppi**
- Integrazione calcolo strutturale completo
- Modulo analisi dinamica avanzata
- API pubbliche per integrazioni
- Mobile app companion
- Sistema collaborativo team

**📅 Ultimo aggiornamento:** 15 Giugno 2025 - ASDP v2.5.0