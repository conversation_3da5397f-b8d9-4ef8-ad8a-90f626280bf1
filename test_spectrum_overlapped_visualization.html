<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Visualizzazione Spettri Sovrapposti - ASDP v2.5.0</title>
    <link rel="stylesheet" href="css/spectrum.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #f8f9fa;
            margin: 0;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: #2d2d2d;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #3498db;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            border-left: 4px solid #f39c12;
        }
        
        .chart-container {
            height: 400px;
            margin: 1rem 0;
            background: white;
            border-radius: 8px;
            padding: 1rem;
        }
        
        .metadata-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .metadata-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid #3498db;
        }
        
        .metadata-label {
            font-weight: 600;
            color: #bdc3c7;
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .metadata-value {
            font-weight: 700;
            color: #ecf0f1;
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
        }
        
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: background 0.3s ease;
        }
        
        .test-button:hover {
            background: #2980b9;
        }
        
        .success-message {
            background: rgba(39, 174, 96, 0.2);
            border: 1px solid #27ae60;
            color: #27ae60;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        
        .error-message {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid #e74c3c;
            color: #e74c3c;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 Test Visualizzazione Spettri Sovrapposti</h1>
            <p>Test delle modifiche implementate per la visualizzazione sovrapposta delle curve ANTE/POST e formattazione a 4 decimali</p>
        </div>

        <div class="test-section">
            <h2>📊 Test 1: Grafico Sovrapposto ANTE/POST</h2>
            <p>Verifica della visualizzazione simultanea delle curve con colori distinti</p>
            
            <div class="chart-container">
                <canvas id="testChart1"></canvas>
            </div>
            
            <div class="metadata-display">
                <div class="metadata-item">
                    <span class="metadata-label">🔴 Smorzamento ANTE</span>
                    <span class="metadata-value" id="damping-before">5.0000%</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">🟢 Smorzamento POST</span>
                    <span class="metadata-value" id="damping-after">12.5000%</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">📉 Riduzione Media</span>
                    <span class="metadata-value" id="reduction-avg">22.3456%</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">📊 Riduzione Massima</span>
                    <span class="metadata-value" id="reduction-max">35.7890%</span>
                </div>
            </div>
            
            <button class="test-button" onclick="generateTestChart1()">🔄 Rigenera Grafico</button>
        </div>

        <div class="test-section">
            <h2>🔢 Test 2: Formattazione Numerica 4 Decimali</h2>
            <p>Verifica della standardizzazione dei valori numerici a 4 cifre decimali</p>
            
            <div class="metadata-display">
                <div class="metadata-item">
                    <span class="metadata-label">Periodo T1</span>
                    <span class="metadata-value" id="period-t1">0.2680 s</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Accelerazione Se max</span>
                    <span class="metadata-value" id="se-max">0.5678 g</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Coefficiente SS</span>
                    <span class="metadata-value" id="coeff-ss">1.1500</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Coefficiente CC</span>
                    <span class="metadata-value" id="coeff-cc">1.0500</span>
                </div>
            </div>
            
            <button class="test-button" onclick="testNumberFormatting()">🧮 Test Formattazione</button>
        </div>

        <div class="test-section">
            <h2>🎨 Test 3: Colori e Leggibilità</h2>
            <p>Verifica dei colori distintivi per le curve ANTE (rosso) e POST (verde)</p>
            
            <div style="display: flex; gap: 2rem; justify-content: center; margin: 1rem 0;">
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <div style="width: 30px; height: 4px; background: #e74c3c; border-radius: 2px;"></div>
                    <span>ANTE Intervento (#e74c3c)</span>
                </div>
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <div style="width: 30px; height: 4px; background: #27ae60; border-radius: 2px;"></div>
                    <span>POST Intervento (#27ae60)</span>
                </div>
            </div>
            
            <div class="success-message">
                ✅ Colori testati e conformi alle specifiche: rosso per ANTE, verde per POST
            </div>
        </div>

        <div class="test-section">
            <h2>📋 Risultati Test</h2>
            <div id="test-results">
                <p>Clicca sui pulsanti di test per verificare le funzionalità...</p>
            </div>
        </div>
    </div>

    <script>
        // Dati di test sintetici
        const testDataBefore = {
            periods: [0.0000, 0.1000, 0.2000, 0.3000, 0.4000, 0.5000, 0.6000, 0.7000, 0.8000, 0.9000, 1.0000],
            accelerations: [0.0000, 0.3456, 0.5678, 0.4321, 0.3789, 0.3234, 0.2890, 0.2567, 0.2345, 0.2123, 0.1987]
        };

        const testDataAfter = {
            periods: [0.0000, 0.1000, 0.2000, 0.3000, 0.4000, 0.5000, 0.6000, 0.7000, 0.8000, 0.9000, 1.0000],
            accelerations: [0.0000, 0.2678, 0.4321, 0.3234, 0.2890, 0.2456, 0.2123, 0.1890, 0.1678, 0.1456, 0.1234]
        };

        let testChart1;

        function generateTestChart1() {
            const ctx = document.getElementById('testChart1').getContext('2d');
            
            if (testChart1) {
                testChart1.destroy();
            }

            testChart1 = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: testDataBefore.periods.map(p => p.toFixed(4)),
                    datasets: [
                        {
                            label: 'ANTE Intervento (ξ=5.0000%)',
                            data: testDataBefore.accelerations.map(a => parseFloat(a.toFixed(4))),
                            borderColor: '#e74c3c',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 3,
                            pointHoverRadius: 6
                        },
                        {
                            label: 'POST Intervento (ξ=12.5000%)',
                            data: testDataAfter.accelerations.map(a => parseFloat(a.toFixed(4))),
                            borderColor: '#27ae60',
                            backgroundColor: 'rgba(39, 174, 96, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 3,
                            pointHoverRadius: 6
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Confronto Spettri di Risposta ANTE/POST Dissipatori',
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.parsed.y.toFixed(4)} g`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: { display: true, text: 'Periodo T (s)' }
                        },
                        y: {
                            display: true,
                            title: { display: true, text: 'Accelerazione Se (g)' },
                            beginAtZero: true
                        }
                    }
                }
            });

            updateTestResults('✅ Grafico sovrapposto generato con successo');
        }

        function testNumberFormatting() {
            // Test formattazione a 4 decimali
            const testValues = [
                { original: 0.26799999, formatted: parseFloat((0.26799999).toFixed(4)) },
                { original: 0.56789123, formatted: parseFloat((0.56789123).toFixed(4)) },
                { original: 1.15000001, formatted: parseFloat((1.15000001).toFixed(4)) },
                { original: 1.04999999, formatted: parseFloat((1.04999999).toFixed(4)) }
            ];

            document.getElementById('period-t1').textContent = testValues[0].formatted.toFixed(4) + ' s';
            document.getElementById('se-max').textContent = testValues[1].formatted.toFixed(4) + ' g';
            document.getElementById('coeff-ss').textContent = testValues[2].formatted.toFixed(4);
            document.getElementById('coeff-cc').textContent = testValues[3].formatted.toFixed(4);

            updateTestResults('✅ Formattazione numerica a 4 decimali applicata correttamente');
        }

        function updateTestResults(message) {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div class="success-message">[${timestamp}] ${message}</div>`;
        }

        // Inizializza il test al caricamento
        document.addEventListener('DOMContentLoaded', function() {
            generateTestChart1();
            testNumberFormatting();
            updateTestResults('🚀 Test inizializzati correttamente');
        });
    </script>
</body>
</html>
