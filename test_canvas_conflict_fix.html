<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fix Conflitto Canvas - ASDP v2.5.1</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="js/SpectrumVisualizerModal.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #f8f9fa;
            margin: 0;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: #2d2d2d;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #3498db;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            border-left: 4px solid #f39c12;
        }
        
        .canvas-container {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            height: 300px;
        }
        
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: background 0.3s ease;
        }
        
        .test-button:hover {
            background: #2980b9;
        }
        
        .test-button.danger {
            background: #e74c3c;
        }
        
        .test-button.danger:hover {
            background: #c0392b;
        }
        
        .success-message {
            background: rgba(39, 174, 96, 0.2);
            border: 1px solid #27ae60;
            color: #27ae60;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        
        .error-message {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid #e74c3c;
            color: #e74c3c;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        
        .log-container {
            background: #1a1a1a;
            border: 1px solid #444;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-entry {
            margin: 0.25rem 0;
            padding: 0.25rem;
        }
        
        .log-success {
            color: #27ae60;
        }
        
        .log-error {
            color: #e74c3c;
        }
        
        .log-info {
            color: #3498db;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 Test Fix Conflitto Canvas</h1>
            <p>Test della risoluzione del problema "Canvas is already in use" per Chart.js</p>
        </div>

        <div class="test-section">
            <h2>📊 Test 1: Canvas Multipli Simultanei</h2>
            <p>Verifica che si possano creare grafici su canvas diversi senza conflitti</p>
            
            <div class="canvas-container">
                <h4>Canvas 1: spectrum-chart-modal</h4>
                <canvas id="spectrum-chart-modal" width="400" height="200"></canvas>
            </div>
            
            <div class="canvas-container">
                <h4>Canvas 2: spectrum-comparison-chart-results</h4>
                <canvas id="spectrum-comparison-chart-results" width="400" height="200"></canvas>
            </div>
            
            <button class="test-button" onclick="testMultipleCanvas()">📈 Test Canvas Multipli</button>
            <button class="test-button" onclick="forceCleanAllCanvas()">🧹 Pulizia Forzata Tutti</button>
            <div id="multiple-canvas-results"></div>
        </div>

        <div class="test-section">
            <h2>🔄 Test 2: Riutilizzo Canvas</h2>
            <p>Verifica che si possa riutilizzare lo stesso canvas senza errori</p>
            
            <div class="canvas-container">
                <h4>Canvas Riutilizzabile</h4>
                <canvas id="reusable-canvas" width="400" height="200"></canvas>
            </div>
            
            <button class="test-button" onclick="testCanvasReuse()">🔄 Test Riutilizzo</button>
            <button class="test-button danger" onclick="forceCanvasConflict()">⚠️ Forza Conflitto</button>
            <div id="canvas-reuse-results"></div>
        </div>

        <div class="test-section">
            <h2>🧹 Test 3: Pulizia Automatica</h2>
            <p>Verifica che la pulizia automatica dei grafici esistenti funzioni correttamente</p>
            
            <div class="canvas-container">
                <h4>Canvas Test Pulizia</h4>
                <canvas id="cleanup-test-canvas" width="400" height="200"></canvas>
            </div>
            
            <button class="test-button" onclick="testAutomaticCleanup()">🧹 Test Pulizia</button>
            <div id="cleanup-results"></div>
        </div>

        <div class="test-section">
            <h2>📋 Log Test</h2>
            <div class="log-container" id="test-log">
                <div class="log-entry log-info">Pronto per i test...</div>
            </div>
            <button class="test-button" onclick="clearLog()">🗑️ Pulisci Log</button>
        </div>
    </div>

    <script>
        // Dati di test
        const testData = {
            labels: ['0.0', '0.1', '0.2', '0.3', '0.4', '0.5'],
            datasets: [{
                label: 'Test Data',
                data: [0, 0.3, 0.5, 0.4, 0.3, 0.2],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                borderWidth: 2,
                fill: false
            }]
        };

        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: { display: true, text: 'Test Chart' },
                legend: { display: true }
            },
            scales: {
                x: { title: { display: true, text: 'Periodo (s)' } },
                y: { title: { display: true, text: 'Accelerazione (g)' }, beginAtZero: true }
            }
        };

        let testCharts = {};

        function log(message, type = 'info') {
            const logContainer = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '<div class="log-entry log-info">Log pulito...</div>';
        }

        function forceCleanAllCanvas() {
            log('Iniziando pulizia forzata di tutti i canvas...', 'info');

            const canvasIds = ['spectrum-chart-modal', 'spectrum-comparison-chart-results', 'reusable-canvas', 'cleanup-test-canvas'];

            canvasIds.forEach(canvasId => {
                try {
                    const canvas = document.getElementById(canvasId);
                    if (canvas) {
                        log(`Pulendo canvas ${canvasId}...`, 'info');

                        // Distruggi tutti i grafici Chart.js associati
                        const existingChart = Chart.getChart(canvas);
                        if (existingChart) {
                            log(`Distruggendo grafico esistente su ${canvasId} (ID: ${existingChart.id})`, 'info');
                            existingChart.destroy();
                        }

                        // Pulizia aggressiva
                        Object.values(Chart.instances || {}).forEach(chart => {
                            if (chart && chart.canvas === canvas) {
                                log(`Distruggendo istanza forzata su ${canvasId}`, 'info');
                                chart.destroy();
                            }
                        });

                        // Reset canvas
                        const ctx = canvas.getContext('2d');
                        ctx.clearRect(0, 0, canvas.width, canvas.height);

                        // Rimuovi attributi Chart.js
                        delete canvas.chartjs;
                        canvas.removeAttribute('data-chartjs-id');

                        log(`Canvas ${canvasId} pulito`, 'success');
                    }
                } catch (error) {
                    log(`Errore pulizia ${canvasId}: ${error.message}`, 'error');
                }
            });

            log('Pulizia forzata completata', 'success');
        }

        function testMultipleCanvas() {
            log('Iniziando test canvas multipli...', 'info');

            try {
                // Test Canvas 1
                log('Creando grafico su spectrum-chart-modal...', 'info');
                const visualizer1 = new SpectrumVisualizerModal('spectrum-chart-modal');

                const testData1 = {
                    ...testData,
                    datasets: [{
                        ...testData.datasets[0],
                        label: 'Canvas 1',
                        borderColor: '#e74c3c'
                    }]
                };

                visualizer1.createChart(testData1, chartOptions);
                log('Grafico 1 creato con successo', 'success');

                // Test Canvas 2 - Attesa più lunga per permettere pulizia
                setTimeout(() => {
                    try {
                        log('Creando grafico su spectrum-comparison-chart-results...', 'info');
                        const visualizer2 = new SpectrumVisualizerModal('spectrum-comparison-chart-results');

                        const testData2 = {
                            ...testData,
                            datasets: [{
                                ...testData.datasets[0],
                                label: 'Canvas 2',
                                borderColor: '#27ae60'
                            }]
                        };

                        visualizer2.createChart(testData2, chartOptions);
                        log('Grafico 2 creato con successo', 'success');

                        document.getElementById('multiple-canvas-results').innerHTML = `
                            <div class="success-message">
                                ✅ Test canvas multipli: SUCCESSO
                                <br>Entrambi i grafici creati senza conflitti
                            </div>
                        `;
                    } catch (error2) {
                        log(`Errore creazione grafico 2: ${error2.message}`, 'error');
                        document.getElementById('multiple-canvas-results').innerHTML = `
                            <div class="error-message">
                                ❌ Test canvas multipli: FALLITO
                                <br>Errore grafico 2: ${error2.message}
                            </div>
                        `;
                    }
                }, 200); // Ridotto timeout per test più veloce

            } catch (error) {
                log(`Errore test canvas multipli: ${error.message}`, 'error');
                document.getElementById('multiple-canvas-results').innerHTML = `
                    <div class="error-message">
                        ❌ Test canvas multipli: FALLITO
                        <br>Errore: ${error.message}
                    </div>
                `;
            }
        }

        function testCanvasReuse() {
            log('Iniziando test riutilizzo canvas...', 'info');

            try {
                const canvasId = 'reusable-canvas';

                // Prima creazione
                log('Prima creazione grafico...', 'info');
                const visualizer1 = new SpectrumVisualizerModal(canvasId);
                visualizer1.createChart(testData, chartOptions);
                log('Prima creazione completata', 'success');

                // Seconda creazione (dovrebbe sostituire la prima)
                setTimeout(() => {
                    try {
                        log('Seconda creazione grafico (riutilizzo canvas)...', 'info');
                        const visualizer2 = new SpectrumVisualizerModal(canvasId);

                        const testData2 = {
                            ...testData,
                            datasets: [{
                                ...testData.datasets[0],
                                label: 'Riutilizzato',
                                borderColor: '#f39c12'
                            }]
                        };

                        visualizer2.createChart(testData2, chartOptions);
                        log('Seconda creazione completata', 'success');

                        document.getElementById('canvas-reuse-results').innerHTML = `
                            <div class="success-message">
                                ✅ Test riutilizzo canvas: SUCCESSO
                                <br>Canvas riutilizzato senza errori
                            </div>
                        `;
                    } catch (error2) {
                        log(`Errore seconda creazione: ${error2.message}`, 'error');
                        document.getElementById('canvas-reuse-results').innerHTML = `
                            <div class="error-message">
                                ❌ Test riutilizzo canvas: FALLITO
                                <br>Errore seconda creazione: ${error2.message}
                            </div>
                        `;
                    }
                }, 300); // Ridotto timeout

            } catch (error) {
                log(`Errore test riutilizzo: ${error.message}`, 'error');
                document.getElementById('canvas-reuse-results').innerHTML = `
                    <div class="error-message">
                        ❌ Test riutilizzo canvas: FALLITO
                        <br>Errore: ${error.message}
                    </div>
                `;
            }
        }

        function forceCanvasConflict() {
            log('Forzando conflitto canvas...', 'info');
            
            try {
                const canvas = document.getElementById('reusable-canvas');
                const ctx = canvas.getContext('2d');
                
                // Crea grafico direttamente senza pulizia
                log('Creando grafico diretto senza pulizia...', 'info');
                const chart1 = new Chart(ctx, {
                    type: 'line',
                    data: testData,
                    options: chartOptions
                });

                // Tenta di creare un secondo grafico (dovrebbe causare errore)
                setTimeout(() => {
                    log('Tentando seconda creazione senza pulizia...', 'info');
                    try {
                        const chart2 = new Chart(ctx, {
                            type: 'line',
                            data: testData,
                            options: chartOptions
                        });
                        log('Seconda creazione riuscita (inaspettato)', 'error');
                    } catch (conflictError) {
                        log(`Conflitto rilevato come atteso: ${conflictError.message}`, 'success');
                        
                        // Ora testa la risoluzione automatica
                        log('Testando risoluzione automatica...', 'info');
                        const visualizer = new SpectrumVisualizerModal('reusable-canvas');
                        visualizer.createChart(testData, chartOptions);
                        log('Risoluzione automatica completata', 'success');
                    }
                }, 1000);

            } catch (error) {
                log(`Errore test conflitto: ${error.message}`, 'error');
            }
        }

        function testAutomaticCleanup() {
            log('Iniziando test pulizia automatica...', 'info');
            
            try {
                const canvasId = 'cleanup-test-canvas';
                
                // Crea più grafici in sequenza rapida
                for (let i = 0; i < 3; i++) {
                    setTimeout(() => {
                        log(`Creando grafico ${i + 1}/3...`, 'info');
                        const visualizer = new SpectrumVisualizerModal(canvasId);
                        
                        const testDataVariant = {
                            ...testData,
                            datasets: [{
                                ...testData.datasets[0],
                                label: `Grafico ${i + 1}`,
                                borderColor: ['#e74c3c', '#27ae60', '#f39c12'][i]
                            }]
                        };
                        
                        visualizer.createChart(testDataVariant, chartOptions);
                        log(`Grafico ${i + 1} creato`, 'success');
                        
                        if (i === 2) {
                            document.getElementById('cleanup-results').innerHTML = `
                                <div class="success-message">
                                    ✅ Test pulizia automatica: SUCCESSO
                                    <br>Tutti i grafici creati senza conflitti
                                </div>
                            `;
                        }
                    }, i * 1000);
                }

            } catch (error) {
                log(`Errore test pulizia: ${error.message}`, 'error');
                document.getElementById('cleanup-results').innerHTML = `
                    <div class="error-message">
                        ❌ Test pulizia automatica: FALLITO
                        <br>Errore: ${error.message}
                    </div>
                `;
            }
        }

        // Inizializzazione
        document.addEventListener('DOMContentLoaded', function() {
            log('Test inizializzati e pronti', 'success');
        });
    </script>
</body>
</html>
