<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report <PERSON><PERSON>i Massa Inerziale - ASDP v2.5.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Chart.js per grafici spettri di risposta -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <style>
    :root {
        --primary-color: #FF7043;
        --bg-color: #1E1E1E;
        --text-color: #FFFFFF;
        --border-color: #333333;
        --hover-color: #FF8A65;
    }

    body {
        margin: 0;
        padding: 0;
        background-color: #FFFFFF;
        color: #000000;
        font-family: 'Segoe UI', Arial, sans-serif;
        line-height: 1.6;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px;
    }

    .header {
        text-align: center;
        margin-bottom: 3rem;
        padding: 2rem;
        background: linear-gradient(135deg, #FF7043 0%, #FF8A65 100%);
        color: white;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(255, 112, 67, 0.3);
    }

    .header h1 {
        margin: 0 0 0.5rem 0;
        font-size: 2.5rem;
        font-weight: 700;
    }

    .header .subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin: 0;
    }

    .header .version {
        font-size: 1rem;
        opacity: 0.8;
        margin-top: 0.5rem;
    }

    .section {
        margin: 2rem 0;
        padding: 2rem;
        background: #f8f9fa;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-left: 4px solid var(--primary-color);
    }

    .section h2 {
        color: var(--primary-color);
        margin-top: 0;
        margin-bottom: 1.5rem;
        font-size: 1.8rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section h3 {
        color: #333;
        margin: 1.5rem 0 1rem 0;
        font-size: 1.3rem;
        font-weight: 600;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
    }

    .info-item {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .info-label {
        font-weight: 600;
        color: #555;
    }

    .info-value {
        font-weight: 700;
        color: var(--primary-color);
    }

    .results-table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .results-table th {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        color: white;
        padding: 1rem 0.75rem;
        text-align: center;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .results-table td {
        padding: 0.875rem 0.75rem;
        text-align: center;
        border-bottom: 1px solid #e0e0e0;
        font-size: 0.9rem;
    }

    .results-table tbody tr:hover {
        background: #f5f5f5;
    }

    .dampers-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 1.5rem 0;
    }

    .damper-summary-item {
        background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.2) 100%);
        border: 1px solid rgba(66, 153, 225, 0.3);
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
    }

    .damper-label {
        display: block;
        color: #666;
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .damper-value {
        display: block;
        color: #4299e1;
        font-size: 1.2rem;
        font-weight: 700;
    }

    .technical-analysis {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        margin: 1rem 0;
        white-space: pre-line;
        line-height: 1.8;
    }

    .building-notes {
        background: rgba(128, 90, 213, 0.1);
        border: 1px solid rgba(128, 90, 213, 0.3);
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
    }

    .building-note {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        margin: 0.75rem 0;
        padding: 0.75rem;
        background: rgba(128, 90, 213, 0.1);
        border-radius: 6px;
        border-left: 3px solid #805ad5;
    }

    .note-icon {
        font-size: 1.1rem;
        color: #805ad5;
        flex-shrink: 0;
    }

    .note-text {
        color: #333;
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .timestamp {
        text-align: center;
        margin: 2rem 0;
        padding: 1rem;
        background: #e8f4fd;
        border-radius: 8px;
        color: #666;
        font-style: italic;
    }

    /* Pulsanti */
    .print-button {
        position: fixed;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 1000;
        bottom: 30px;
        right: 30px;
    }

    .close-button {
        position: fixed;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 1000;
        top: 20px;
        right: 20px;
    }

    .print-button:hover, .close-button:hover {
        background: var(--hover-color);
        transform: scale(1.1);
        box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    }

    @media print {
        .print-button, .close-button {
            display: none;
        }
        
        .container {
            padding: 20px;
        }
        
        .section {
            box-shadow: none;
            border: 1px solid #ddd;
            page-break-inside: avoid;
        }
        
        .header {
            background: #FF7043 !important;
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
        }
    }

    /* =====================================================
       SEZIONE SPETTRI DI RISPOSTA
       ===================================================== */

    .spectra-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin: 2rem 0;
    }

    .spectrum-chart-wrapper {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border: 1px solid #e0e0e0;
    }

    .spectrum-chart-wrapper h4 {
        color: var(--primary-color);
        margin: 0 0 1rem 0;
        font-size: 1.2rem;
        font-weight: 600;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .spectrum-chart-container {
        position: relative;
        height: 350px;
        margin-bottom: 1rem;
    }

    .spectrum-metadata {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .spectrum-metadata h5 {
        color: #333;
        margin: 0 0 0.75rem 0;
        font-size: 1rem;
        font-weight: 600;
    }

    .metadata-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.75rem;
    }

    .metadata-item {
        background: white;
        padding: 0.75rem;
        border-radius: 6px;
        border: 1px solid #e0e0e0;
        text-align: center;
    }

    .metadata-label {
        display: block;
        font-size: 0.8rem;
        color: #666;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .metadata-value {
        display: block;
        font-size: 1rem;
        color: var(--primary-color);
        font-weight: 700;
    }

    .reduction-analysis {
        background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.2) 100%);
        border: 1px solid rgba(34, 197, 94, 0.3);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: center;
    }

    .reduction-title {
        color: #059669;
        font-size: 1.3rem;
        font-weight: 700;
        margin: 0 0 1rem 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .reduction-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .reduction-stat {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid rgba(34, 197, 94, 0.2);
    }

    .reduction-stat-label {
        display: block;
        font-size: 0.9rem;
        color: #666;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .reduction-stat-value {
        display: block;
        font-size: 1.4rem;
        color: #059669;
        font-weight: 700;
    }

    @media (max-width: 768px) {
        .container {
            padding: 20px;
        }

        .header h1 {
            font-size: 2rem;
        }

        .info-grid {
            grid-template-columns: 1fr;
        }

        .dampers-summary {
            grid-template-columns: 1fr;
        }

        .results-table {
            font-size: 0.8rem;
        }

        .results-table th,
        .results-table td {
            padding: 0.5rem 0.25rem;
        }

        .spectra-container {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .spectrum-chart-container {
            height: 300px;
        }

        .metadata-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .reduction-stats {
            grid-template-columns: 1fr;
        }
    }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-building"></i> Report Analisi Massa Inerziale</h1>
            <p class="subtitle">Sistema Avanzato di Analisi Sismica e Raccomandazioni Dissipatori</p>
            <p class="version">ASDP v2.5.0 - {{TIMESTAMP}}</p>
        </div>

        <!-- Sezione 1: Dati di Input -->
        <div class="section">
            <h2><i class="fas fa-map-marker-alt"></i> Dati di Input</h2>
            
            <h3>Localizzazione e Parametri Sismici</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">Coordinate:</span>
                    <span class="info-value">{{COORDINATES}}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Zona Sismica:</span>
                    <span class="info-value">{{SEISMIC_ZONE}}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">ag (accelerazione al suolo):</span>
                    <span class="info-value">{{AG}} g</span>
                </div>
                <div class="info-item">
                    <span class="info-label">F0:</span>
                    <span class="info-value">{{F0}}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">TC*:</span>
                    <span class="info-value">{{TC}} s</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Categoria Suolo:</span>
                    <span class="info-value">{{SOIL_CATEGORY}}</span>
                </div>
            </div>

            <h3>Caratteristiche Strutturali</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">Tipologia Costruttiva:</span>
                    <span class="info-value">{{CONSTRUCTION_CATEGORY}}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Tipologia Strutturale:</span>
                    <span class="info-value">{{STRUCTURE_TYPE}}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Tipologia Solaio:</span>
                    <span class="info-value">{{SLAB_TYPE}}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Anno di Costruzione:</span>
                    <span class="info-value">{{CONSTRUCTION_YEAR}}</span>
                </div>
            </div>
        </div>

        <!-- Sezione 2: Risultati Calcolo Massa Inerziale -->
        <div class="section">
            <h2><i class="fas fa-calculator"></i> Risultati Calcolo Massa Inerziale</h2>
            
            <h3>Riepilogo Generale</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">💪 Massa Totale:</span>
                    <span class="info-value">{{TOTAL_MASS}} t</span>
                </div>
                <div class="info-item">
                    <span class="info-label">⏱️ Periodo Fondamentale:</span>
                    <span class="info-value">{{PERIOD}} s</span>
                </div>
                <div class="info-item">
                    <span class="info-label">⚡ Forza Sismica Totale:</span>
                    <span class="info-value">{{TOTAL_FORCE}} kN</span>
                </div>
                <div class="info-item">
                    <span class="info-label">📊 Spettro di Risposta:</span>
                    <span class="info-value">{{RESPONSE_SPECTRUM}}</span>
                </div>
            </div>

            <h3>Distribuzione Forze per Piano</h3>
            <table class="results-table">
                <thead>
                    <tr>
                        <th>🏢 PIANO</th>
                        <th>⚖️ MASSA (T)</th>
                        <th>📏 ALTEZZA (M)</th>
                        <th>🏠 DESTINAZIONE</th>
                        <th>⭐ FORZA (KN)</th>
                    </tr>
                </thead>
                <tbody>
                    {{FLOOR_FORCES_TABLE}}
                </tbody>
            </table>
        </div>

        <!-- Sezione 3: Raccomandazioni Dissipatori Sismici -->
        <div class="section">
            <h2><i class="fas fa-cogs"></i> Raccomandazioni Dissipatori Sismici</h2>
            
            <h3>Riepilogo Raccomandazione</h3>
            <div class="dampers-summary">
                <div class="damper-summary-item">
                    <span class="damper-label">🏗️ Massa Inerziale</span>
                    <span class="damper-value">{{TOTAL_MASS}} t</span>
                </div>
                <div class="damper-summary-item">
                    <span class="damper-label">⚡ Dissipazione Richiesta</span>
                    <span class="damper-value">{{REQUIRED_DISSIPATION}} kN</span>
                </div>
                <div class="damper-summary-item">
                    <span class="damper-label">🎯 Efficienza Raggiunta</span>
                    <span class="damper-value">{{EFFICIENCY_RATIO}}%</span>
                </div>
                <div class="damper-summary-item">
                    <span class="damper-label">📊 Totale Dissipatori</span>
                    <span class="damper-value">{{TOTAL_DAMPERS}} unità</span>
                </div>
            </div>

            <h3>Raccomandazione Ottimale</h3>
            <table class="results-table">
                <thead>
                    <tr>
                        <th>🔧 TIPOLOGIA</th>
                        <th>⚡ CAPACITÀ UNITARIA</th>
                        <th>📊 QUANTITÀ</th>
                        <th>💪 CAPACITÀ TOTALE</th>
                    </tr>
                </thead>
                <tbody>
                    {{DAMPERS_TABLE}}
                </tbody>
            </table>

            <h3>Analisi Tecnica</h3>
            <div class="technical-analysis">{{TECHNICAL_EXPLANATION}}</div>

            <h3>Note Specifiche per l'Edificio</h3>
            <div class="building-notes">
                {{BUILDING_NOTES}}
            </div>
        </div>

        <!-- Sezione 4: Spettri di Risposta -->
        <div class="section">
            <h2><i class="fas fa-chart-line"></i> Spettri di Risposta - Analisi Efficacia Dissipatori</h2>

            <p style="color: #666; font-size: 1rem; line-height: 1.6; margin-bottom: 2rem;">
                I grafici seguenti mostrano il confronto tra gli spettri di risposta <strong>prima</strong> e <strong>dopo</strong>
                l'installazione dei dissipatori sismici raccomandati. La riduzione delle accelerazioni spettrali dimostra
                l'efficacia del sistema di dissipazione proposto secondo le NTC 2018.
            </p>

            <!-- Grafico Sovrapposto -->
            <div class="spectrum-chart-wrapper" style="grid-column: 1 / -1; margin-bottom: 2rem;">
                <h4><i class="fas fa-chart-line" style="color: #3498db;"></i> Confronto Spettri ANTE/POST Intervento</h4>
                <div class="spectrum-chart-container" style="height: 400px;">
                    <canvas id="spectrumChartComparison"></canvas>
                </div>
                <div class="spectrum-metadata">
                    <h5>Analisi Confronto</h5>
                    <div class="metadata-grid">
                        <div class="metadata-item">
                            <span class="metadata-label">🔴 Smorzamento ANTE</span>
                            <span class="metadata-value">{{DAMPING_BEFORE}}%</span>
                        </div>
                        <div class="metadata-item">
                            <span class="metadata-label">🟢 Smorzamento POST</span>
                            <span class="metadata-value">{{DAMPING_AFTER}}%</span>
                        </div>
                        <div class="metadata-item">
                            <span class="metadata-label">📉 Riduzione Media</span>
                            <span class="metadata-value">{{REDUCTION_AVERAGE}}%</span>
                        </div>
                        <div class="metadata-item">
                            <span class="metadata-label">📊 Riduzione Massima</span>
                            <span class="metadata-value">{{REDUCTION_MAX}}%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Grafici Separati (Opzionale) -->
            <div class="spectra-container">
                <!-- Spettro ANTE Intervento -->
                <div class="spectrum-chart-wrapper">
                    <h4><i class="fas fa-exclamation-triangle" style="color: #e74c3c;"></i> ANTE Intervento</h4>
                    <div class="spectrum-chart-container">
                        <canvas id="spectrumChartBefore"></canvas>
                    </div>
                    <div class="spectrum-metadata">
                        <h5>Parametri Struttura Esistente</h5>
                        <div class="metadata-grid">
                            <div class="metadata-item">
                                <span class="metadata-label">Smorzamento</span>
                                <span class="metadata-value">{{DAMPING_BEFORE}}%</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">SS</span>
                                <span class="metadata-value">{{SS_COEFF}}</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">CC</span>
                                <span class="metadata-value">{{CC_COEFF}}</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">S</span>
                                <span class="metadata-value">{{S_COEFF}}</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">TB</span>
                                <span class="metadata-value">{{TB_PERIOD}} s</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">TC</span>
                                <span class="metadata-value">{{TC_PERIOD}} s</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">TD</span>
                                <span class="metadata-value">{{TD_PERIOD}} s</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">Se max</span>
                                <span class="metadata-value">{{SE_MAX_BEFORE}} g</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Spettro POST Intervento -->
                <div class="spectrum-chart-wrapper">
                    <h4><i class="fas fa-shield-alt" style="color: #27ae60;"></i> POST Intervento</h4>
                    <div class="spectrum-chart-container">
                        <canvas id="spectrumChartAfter"></canvas>
                    </div>
                    <div class="spectrum-metadata">
                        <h5>Parametri con Dissipatori</h5>
                        <div class="metadata-grid">
                            <div class="metadata-item">
                                <span class="metadata-label">Smorzamento</span>
                                <span class="metadata-value">{{DAMPING_AFTER}}%</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">SS</span>
                                <span class="metadata-value">{{SS_COEFF}}</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">CC</span>
                                <span class="metadata-value">{{CC_COEFF}}</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">S</span>
                                <span class="metadata-value">{{S_COEFF}}</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">TB</span>
                                <span class="metadata-value">{{TB_PERIOD}} s</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">TC</span>
                                <span class="metadata-value">{{TC_PERIOD}} s</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">TD</span>
                                <span class="metadata-value">{{TD_PERIOD}} s</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">Se max</span>
                                <span class="metadata-value">{{SE_MAX_AFTER}} g</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analisi Riduzione -->
            <div class="reduction-analysis">
                <h3 class="reduction-title">
                    <i class="fas fa-chart-bar"></i> Analisi Riduzione Accelerazioni
                </h3>
                <div class="reduction-stats">
                    <div class="reduction-stat">
                        <span class="reduction-stat-label">Riduzione Media</span>
                        <span class="reduction-stat-value">{{REDUCTION_AVERAGE}}%</span>
                    </div>
                    <div class="reduction-stat">
                        <span class="reduction-stat-label">Riduzione Massima</span>
                        <span class="reduction-stat-value">{{REDUCTION_MAX}}%</span>
                    </div>
                    <div class="reduction-stat">
                        <span class="reduction-stat-label">Riduzione al Periodo T1</span>
                        <span class="reduction-stat-value">{{REDUCTION_T1}}%</span>
                    </div>
                    <div class="reduction-stat">
                        <span class="reduction-stat-label">Efficienza Dissipatori</span>
                        <span class="reduction-stat-value">{{DAMPER_EFFICIENCY}}%</span>
                    </div>
                </div>
                <p style="margin-top: 1rem; color: #059669; font-weight: 600; font-size: 1rem;">
                    <i class="fas fa-check-circle"></i>
                    I dissipatori raccomandati garantiscono una riduzione significativa delle accelerazioni spettrali,
                    migliorando la risposta sismica dell'edificio e riducendo le sollecitazioni strutturali.
                </p>
            </div>
        </div>

        <!-- Sezione 5: Analisi AI -->
        {{AI_ANALYSIS_SECTION}}

        <!-- Timestamp -->
        <div class="timestamp">
            <i class="fas fa-clock"></i> Report generato il {{FULL_TIMESTAMP}} - Calcolo ID: {{CALCULATION_ID}}
        </div>
    </div>

    <!-- Pulsanti -->
    <button class="print-button" id="printBtn" title="Stampa report">
        <i class="fas fa-print"></i>
    </button>

    <button class="close-button" id="closeBtn" title="Chiudi">
        <i class="fas fa-times"></i>
    </button>

    <script>
        // =====================================================
        // GENERAZIONE GRAFICI SPETTRI DI RISPOSTA
        // =====================================================

        // Dati spettri (verranno sostituiti dal PHP)
        const spectrumDataBefore = {{SPECTRUM_DATA_BEFORE}};
        const spectrumDataAfter = {{SPECTRUM_DATA_AFTER}};

        // Configurazione grafici Chart.js
        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        font: {
                            size: 12,
                            weight: '600'
                        },
                        color: '#333'
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#ddd',
                    borderWidth: 1,
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${context.parsed.y.toFixed(4)} g`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Periodo T (s)',
                        font: {
                            size: 14,
                            weight: '600'
                        },
                        color: '#333'
                    },
                    ticks: {
                        color: '#666',
                        font: {
                            size: 11
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)',
                        lineWidth: 1
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Accelerazione Se (g)',
                        font: {
                            size: 14,
                            weight: '600'
                        },
                        color: '#333'
                    },
                    beginAtZero: true,
                    ticks: {
                        color: '#666',
                        font: {
                            size: 11
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)',
                        lineWidth: 1
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            }
        };

        // Funzione per creare grafico spettro singolo
        function createSpectrumChart(canvasId, spectrumData, title, color) {
            const ctx = document.getElementById(canvasId).getContext('2d');

            return new Chart(ctx, {
                type: 'line',
                data: {
                    labels: spectrumData.periods.map(p => p.toFixed(4)),
                    datasets: [{
                        label: title,
                        data: spectrumData.accelerations.map(a => parseFloat(a.toFixed(4))),
                        borderColor: color,
                        backgroundColor: color.replace('rgb', 'rgba').replace(')', ', 0.1)'),
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 6,
                        pointHoverBackgroundColor: color,
                        pointHoverBorderColor: '#fff',
                        pointHoverBorderWidth: 2
                    }]
                },
                options: chartOptions
            });
        }

        // Funzione per creare grafico confronto sovrapposto
        function createComparisonChart(canvasId, spectrumDataBefore, spectrumDataAfter) {
            const ctx = document.getElementById(canvasId).getContext('2d');

            return new Chart(ctx, {
                type: 'line',
                data: {
                    labels: spectrumDataBefore.periods.map(p => p.toFixed(4)),
                    datasets: [
                        {
                            label: 'ANTE Intervento (ξ={{DAMPING_BEFORE}}%)',
                            data: spectrumDataBefore.accelerations.map(a => parseFloat(a.toFixed(4))),
                            borderColor: 'rgb(231, 76, 60)',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 2,
                            pointHoverRadius: 6,
                            pointHoverBackgroundColor: 'rgb(231, 76, 60)',
                            pointHoverBorderColor: '#fff',
                            pointHoverBorderWidth: 2
                        },
                        {
                            label: 'POST Intervento (ξ={{DAMPING_AFTER}}%)',
                            data: spectrumDataAfter.accelerations.map(a => parseFloat(a.toFixed(4))),
                            borderColor: 'rgb(39, 174, 96)',
                            backgroundColor: 'rgba(39, 174, 96, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 2,
                            pointHoverRadius: 6,
                            pointHoverBackgroundColor: 'rgb(39, 174, 96)',
                            pointHoverBorderColor: '#fff',
                            pointHoverBorderWidth: 2
                        }
                    ]
                },
                options: {
                    ...chartOptions,
                    plugins: {
                        ...chartOptions.plugins,
                        title: {
                            display: true,
                            text: 'Confronto Spettri di Risposta ANTE/POST Dissipatori',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.parsed.y.toFixed(4)} g`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Inizializzazione grafici quando il DOM è pronto
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Inizializzando grafici spettri di risposta...');

            try {
                // Verifica che Chart.js sia disponibile
                if (typeof Chart === 'undefined') {
                    console.error('Chart.js non disponibile');
                    return;
                }

                // Verifica che i dati siano disponibili
                if (!spectrumDataBefore || !spectrumDataAfter) {
                    console.error('Dati spettri non disponibili');
                    return;
                }

                // Crea grafico confronto sovrapposto
                const chartComparison = createComparisonChart(
                    'spectrumChartComparison',
                    spectrumDataBefore,
                    spectrumDataAfter
                );

                // Crea grafico ANTE intervento
                const chartBefore = createSpectrumChart(
                    'spectrumChartBefore',
                    spectrumDataBefore,
                    'Spettro Elastico ANTE',
                    'rgb(231, 76, 60)'
                );

                // Crea grafico POST intervento
                const chartAfter = createSpectrumChart(
                    'spectrumChartAfter',
                    spectrumDataAfter,
                    'Spettro Elastico POST',
                    'rgb(39, 174, 96)'
                );

                console.log('Grafici spettri inizializzati con successo');

                // Ottimizzazione per stampa
                window.addEventListener('beforeprint', function() {
                    chartComparison.resize();
                    chartBefore.resize();
                    chartAfter.resize();
                });

            } catch (error) {
                console.error('Errore inizializzazione grafici spettri:', error);
            }
        });

        // =====================================================
        // GESTIONE PULSANTI REPORT
        // =====================================================

        // Gestione pulsante stampa
        document.getElementById('printBtn').addEventListener('click', function() {
            window.print();
        });

        // Gestione pulsante chiusura
        document.getElementById('closeBtn').addEventListener('click', function() {
            // Verifica se siamo in un iframe o finestra popup
            if (window !== window.top) {
                // Siamo in un iframe
                window.parent.postMessage('closeReport', '*');
            } else if (window.opener) {
                // Siamo in una finestra popup
                window.close();
            } else {
                // Fallback: torna alla pagina precedente
                window.history.back();
            }
        });

        // Messaggio di conferma prima della chiusura
        window.addEventListener('beforeunload', function(e) {
            if (window.opener) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>
</body>
</html>
